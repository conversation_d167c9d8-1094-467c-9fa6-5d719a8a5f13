# v7

import os
import javalang
from collections import defaultdict
import json

def extract_relations_to_json_javalang(project_path):
    nodes = {}  # node_id -> metadata
    relations = []
    collection_entities = {}  # full class name → collection
    file_uses_collections = defaultdict(set)
    class_uses_classes = defaultdict(set)

    def register_node(raw_node, file_path):
        if raw_node and raw_node not in nodes:
            node_type, full_name = raw_node.split(":", 1)
            if node_type == "file" or "." not in full_name:
                short_name = os.path.basename(full_name)
            else:
                short_name = full_name.split(".")[-1] if "." in full_name else full_name

            nodes[raw_node] = {
                "id": raw_node,
                "type": node_type,
                "name": short_name,
                "full_name": full_name,
                "file_path": file_path
            }

    def add_relation(src, rel, dst, file_path):
        if src is None or dst is None:
            return
        register_node(src, file_path)
        register_node(dst, file_path)
        relations.append([src, rel, dst])

    java_files = []
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        rel_root = os.path.join(project_path, rel_root)
        folder_node = f"folder:{rel_root}" if rel_root != "." else f"folder:{os.path.basename(project_path)}"

        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_rel = os.path.join(project_path, subfolder_rel)
            folder_path = os.path.dirname(subfolder_rel)
            subfolder_node = f"folder:{subfolder_rel}"

            add_relation(folder_node, "contains", subfolder_node, rel_root)

        for file in files:
            if file.endswith(".java"):
                java_files.append(os.path.join(root, file))

    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                parsed_files[file_path] = javalang.parse.parse(f.read())
            except javalang.parser.JavaSyntaxError:
                continue

    for file_path, tree in parsed_files.items():
        import_map = {}
        package_name = tree.package.name if tree.package else None

        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")):
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        for type_decl in tree.types:
            if not isinstance(type_decl, javalang.tree.ClassDeclaration):
                continue

            class_name = type_decl.name
            full_class_name = f"{package_name}.{class_name}" if package_name else class_name
            class_node = f"class:{full_class_name}"
            for annotation in type_decl.annotations:
                if annotation.name == "Document":
                    for pair in annotation.element:
                        if pair.name == "collection":
                            collection = pair.value.value
                            collection_entities[full_class_name] = collection
                            rel_path = os.path.relpath(file_path, project_path)
                            add_relation(class_node, "mapped_to_collection", f"collection:{collection}", rel_path)

    for file_path, tree in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        rel_path = os.path.join(project_path, rel_path)
        folder_path = os.path.dirname(rel_path)
        folder_node = f"folder:{folder_path}" if folder_path != "." else f"folder:{project_path}"
        file_node = f"file:{rel_path}"

        add_relation(folder_node, "contains", file_node, rel_path)

        import_map = {}
        package_name = tree.package.name if tree.package else None

        for imp in tree.imports:
            if imp.path and not imp.wildcard and not imp.path.startswith(("java.", "javax.")):
                class_name = imp.path.split('.')[-1]
                import_map[class_name] = imp.path

        for type_decl in tree.types:
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            decl_node = f"{decl_type}:{full_decl_name}"
            add_relation(file_node, "declares", decl_node, rel_path)

            # Handle implements (class → interface)
            if isinstance(type_decl, javalang.tree.ClassDeclaration) and type_decl.implements:
                for impl in type_decl.implements:
                    interface_name = impl.name
                    if interface_name in import_map:
                        impl_full = import_map[interface_name]
                        add_relation(decl_node, "implements", f"interface:{impl_full}", rel_path)

            # Handle extends (class or interface)
            if type_decl.extends:
                if isinstance(type_decl.extends, list):
                    for ext in type_decl.extends:
                        if ext.name in import_map:
                            ext_full = import_map[ext.name]
                            add_relation(decl_node, "extends", f"{decl_type}:{ext_full}", rel_path)
                else:
                    ext = type_decl.extends
                    if ext.name in import_map:
                        ext_full = import_map[ext.name]
                        add_relation(decl_node, "extends", f"{decl_type}:{ext_full}", rel_path)

            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_name = decl.name
                    var_node = f"variable:{full_decl_name}.{var_name}"
                    add_relation(decl_node, "has_variable", var_node, rel_path)

                    if hasattr(field.type, 'name') and field.type.name in import_map:
                        imp_class = import_map[field.type.name]
                        # add_relation(decl_node, "uses_class", f"class:{imp_class}", rel_path)
                        class_uses_classes[full_decl_name].add(imp_class)

                        if imp_class in collection_entities:
                            collection = collection_entities[imp_class]
                            add_relation(var_node, "uses_collection", f"collection:{collection}", rel_path)

            for method in getattr(type_decl, "methods", []):
                method_node = f"method:{full_decl_name}.{method.name}"
                add_relation(decl_node, "has_method", method_node, rel_path)

                if not method.body:
                    continue

                declared_var_types = {}


                # local variabels
                for path, node in method:
                    if isinstance(node, javalang.tree.LocalVariableDeclaration):
                        for decl in node.declarators:
                            var_name = decl.name
                            var_node = f"variable:{full_decl_name}.{method.name}.{var_name}"
                            add_relation(method_node, "uses", var_node, rel_path)

                            if hasattr(node.type, 'name'):
                                type_name = node.type.name
                                declared_var_types[var_name] = type_name
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)
                                    class_uses_classes[full_decl_name].add(imp_class)

                                if hasattr(node.type, 'arguments') and node.type.arguments:
                                    for arg in node.type.arguments:
                                        if hasattr(arg, 'type') and hasattr(arg.type, 'name'):
                                            generic_type = arg.type.name
                                            if generic_type in import_map:
                                                imp_class = import_map[generic_type]
                                                add_relation(var_node, "instance_of", f"class:{imp_class}", rel_path)

                    elif isinstance(node, javalang.tree.MethodInvocation):
                        skip_standard = False
                        called_method_node = None
                        if node.qualifier and node.arguments:
                            qualifier = node.qualifier
                            for arg in node.arguments:
                                if isinstance(arg, javalang.tree.MemberReference):
                                    src = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                                    dst = f"variable:{full_decl_name}.{method.name}.{qualifier}"
                                    add_relation(src, "flows_to", dst, rel_path)

                        if node.qualifier and node.qualifier in declared_var_types:
                            type_name = declared_var_types[node.qualifier]
                            if type_name in import_map:
                                imp_class = import_map[type_name]
                                called_method_node = f"method:{imp_class}.{node.member}"
                            else:
                                skip_standard = True
                        elif node.qualifier:
                            if node.qualifier[0].islower():
                                skip_standard = True
                            else:
                                called_method_node = f"method:{node.qualifier}.{node.member}"
                        else:
                            called_method_node = f"method:{full_decl_name}.{node.member}"

                        if not skip_standard and called_method_node:
                            add_relation(method_node, "calls", called_method_node, rel_path)

                        for arg in node.arguments:
                            if isinstance(arg, javalang.tree.MemberReference):
                                var_node = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                            elif isinstance(arg, str):
                                var_node = f"literal:{arg}"
                            else:
                                continue
                            add_relation(called_method_node, "uses", var_node, rel_path)

                        if node.qualifier:
                            qualifier_var = f"variable:{full_decl_name}.{method.name}.{node.qualifier}"
                            if node.qualifier in declared_var_types:
                                type_name = declared_var_types[node.qualifier]
                                if type_name in import_map:
                                    imp_class = import_map[type_name]
                                    add_relation(qualifier_var, "instance_of", f"class:{imp_class}", rel_path)
                                    class_uses_classes[full_decl_name].add(imp_class)

                    elif isinstance(node, javalang.tree.Assignment):
                        left = getattr(node, 'expressionl', None) or getattr(node, 'left', None)
                        right = node.value

                        if isinstance(left, javalang.tree.MemberReference):
                            left_name = left.member
                        elif isinstance(left, str):
                            left_name = left
                        else:
                            continue

                        left_var = f"variable:{full_decl_name}.{method.name}.{left_name}"
                        add_relation(method_node, "declares", left_var, rel_path)

                        if isinstance(right, javalang.tree.MethodInvocation):
                            if right.qualifier:
                                called_method_node = f"method:{right.qualifier}.{right.member}"
                            else:
                                called_method_node = f"method:{full_decl_name}.{right.member}"
                            add_relation(called_method_node, "assigns", left_var, rel_path)
                            add_relation(method_node, "calls", called_method_node, rel_path)

    return {
        "nodes": list(nodes.values()),
        "relations": relations
    }


project_path = "OneInsights"
graph_data = extract_relations_to_json_javalang(project_path)


json_path = "graph_30_07_final.json"
with open(json_path, "w") as f:
    json.dump(graph_data, f, indent=2)

import json
from neo4j import GraphDatabase

def load_json_to_neo4j(json_path, uri, user, password, db):
    # Load JSON content
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    nodes = data["nodes"]
    relations = data["relations"]

    driver = GraphDatabase.driver(uri, auth=(user, password))

    nodes = data["nodes"]
    relations = data["relations"]

    driver = GraphDatabase.driver(uri, auth=(user, password))

    def import_data(tx):
        # Create typed nodes
        for node in nodes:
            label = node["type"].capitalize()  # e.g., 'class' → 'Class'
            tx.run(f"""
                MERGE (n:{label} {{id: $id}})
                SET n.name = $name,
                    n.full_name = $full_name
            """, id=node["id"], name=node["name"], full_name=node["full_name"])

        # Create typed relationships
        for src_id, rel_type, dst_id in relations:
            # Use dynamic Cypher for relationship type
            tx.run(f"""
                MATCH (src {{id: $src_id}})
                MATCH (dst {{id: $dst_id}})
                MERGE (src)-[:`{rel_type}`]->(dst)
            """, src_id=src_id, dst_id=dst_id)


    def clean_db(tx):
        tx.run("MATCH (n) DETACH DELETE n")


    with driver.session(database=db) as session:
        session.execute_write(clean_db)
        session.execute_write(import_data)

    driver.close()
    print("Graph imported to Neo4j.")



# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"

load_json_to_neo4j(
    json_path=json_path,               
    uri=NEO4J_URI,          
    user="neo4j",                    
    password=NEO4J_PASSWORD,
    db= "final"
)
