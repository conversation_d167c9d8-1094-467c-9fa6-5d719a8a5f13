#!/usr/bin/env python3
import json
import sys

try:
    with open('final_v11.ipynb', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("✅ JSON is valid!")
    print(f"📊 Notebook has {len(data['cells'])} cells")
    print("🎉 The notebook should now open properly in VS Code!")
    
except json.JSONDecodeError as e:
    print(f"❌ JSON Error: {e}")
    print(f"   Line: {e.lineno}, Column: {e.colno}")
    print(f"   Position: {e.pos}")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
