import pandas as pd

df_final = pd.read_csv('oneinsights_v10.csv')


from langchain_community.graphs import Neo4jGraph
# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "jai-full"

# Initialize connections
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)

from tqdm import tqdm

# ========== STAGE 7: NEO4J UPLOAD ==========

def upload_to_neo4j(df_final):
    """Upload relationships to Neo4j database with improved variable display"""
    # Collect unique nodes
    unique_nodes = set()
    for _, row in df_final.iterrows():
        unique_nodes.add((row['source_node'], row['source_type']))
        unique_nodes.add((row['destination_node'], row['destination_type']))
    
    print(f"Creating {len(unique_nodes)} unique nodes...")
    
    # Create nodes with proper variable display
    for node_name, node_type in tqdm(unique_nodes, desc="Creating nodes"):
        if node_type == 'Variable' and '.' in node_name:
            # For variables, show only the variable name, store context as property
            context_part, var_part = node_name.split('.', 1)
            create_query = f"MERGE (n:{node_type} {{name: '{var_part}', context: '{context_part}', full_name: '{node_name}'}})"
        else:
            # For other node types, use the full name
            create_query = f"MERGE (n:{node_type} {{name: '{node_name}'}})"
        
        try:
            graph.query(create_query)
        except Exception as e:
            print(f"Error creating node {node_name} ({node_type}): {e}")
    
    # Create relationships with proper variable matching
    for _, row in tqdm(df_final.iterrows(), desc="Creating relationships", total=len(df_final)):
        # Handle variable nodes differently
        source_match = f"{{full_name: '{row['source_node']}'}}" if row['source_type'] == 'Variable' and '.' in row['source_node'] else f"{{name: '{row['source_node']}'}}"
        target_match = f"{{full_name: '{row['destination_node']}'}}" if row['destination_type'] == 'Variable' and '.' in row['destination_node'] else f"{{name: '{row['destination_node']}'}}"
        
        create_rel_query = f"""
        MATCH (s:{row['source_type']} {source_match})
        MATCH (t:{row['destination_type']} {target_match})
        MERGE (s)-[:{row['relationship']}]->(t)
        """
        try:
            graph.query(create_rel_query)
        except Exception as e:
            print(f"Error creating relationship: {e}")
    
    print(f"✅ Neo4j upload complete: {len(unique_nodes)} nodes, {len(df_final)} relationships")


# Execute Neo4j upload
upload_to_neo4j(df_final)

# Final memory save
#save_memory(memory)

print("\n========== PIPELINE COMPLETE ==========")
print(f"✅ Stage 7 Complete: Data uploaded to Neo4j")
print(f"📊 Final relationships: {len(df_final)}")
print(f"💾 Memory file: {MEMORY_FILE}")
print(f"📄 CSV output: java_lineage_v9.csv")
print(f"🗄️ Neo4j database: {NEO4J_DB}")
print("========================================")
