import os
import re
import javalang
import csv
from collections import defaultdict
from neo4j import GraphDatabase

# ---------- Neo4j Config ----------
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"

# ---------- Node & Relation Helpers ----------
def normalize_node(node_type, full_name, file_path, class_name=None, method_name=None):
    short_name = os.path.basename(full_name) if node_type in ("file","folder") else full_name.split(".")[-1]
    if node_type == "file" and short_name.endswith(".java"):
        short_name = short_name[:-5]
    return {
        "id": f"{node_type}:{full_name}",
        "type": node_type.upper(),
        "name": short_name,
        "full_name": full_name,
        "file_path": file_path,
        "class_name": class_name,
        "method_name": method_name
    }

def add_node(nodes, node_type, full_name, file_path, class_name=None, method_name=None):
    node_id = f"{node_type}:{full_name}"
    if node_id not in nodes:
        nodes[node_id] = normalize_node(node_type, full_name, file_path, class_name, method_name)
    return node_id

def add_relation(relations, src, rel, dst, op=None):
    if src and dst:
        relations.add((src, rel.upper(), dst, op))

# ---------- CSV Export ----------
def save_graph_to_csv(nodes, relations, output_dir="graph_csv"):
    os.makedirs(output_dir, exist_ok=True)
    with open(os.path.join(output_dir, "nodes.csv"), "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["id","type","name","full_name","file_path","class_name","method_name"])
        writer.writeheader()
        writer.writerows(nodes.values())

    with open(os.path.join(output_dir, "relations.csv"), "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["src","rel","dst","op"])
        for src,rel,dst,op in relations:
            writer.writerow([src,rel,dst,op or ""])


MAPPING_PATTERNS = {
    'GetMapping': r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
    'PostMapping': r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
    'PutMapping': r'@PutMapping\s*\(\s*["\']([^"\']+)["\']',
    'DeleteMapping': r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']',
    'RequestMapping': r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']'
}

def remove_java_comments(code: str) -> str:
    code = re.sub(r'/\*.*?\*/', '', code, flags=re.DOTALL)
    code = re.sub(r'//.*', '', code)
    return code

def extract_api_calls(code):
    """Extract REST endpoints from Spring annotations"""
    apis = set()
    cleaned = remove_java_comments(code)
    for mapping_type, pattern in MAPPING_PATTERNS.items():
        for match in re.findall(pattern, cleaned, re.MULTILINE):
            path = match.strip()
            if path and path.startswith("/"):
                apis.add(path)
    return apis

def extract_db_table_usage_clean(code):
    """Clean SQL table usage"""
    tables = set()
    cleaned = remove_java_comments(code)
    for kw in ["FROM","INTO","UPDATE","JOIN"]:
        for m in re.finditer(rf'\b{kw}\s+([A-Za-z_][A-Za-z0-9_]*)', cleaned, flags=re.IGNORECASE):
            table = m.group(1)
            if table.lower() not in {"select","where","group","order"}:
                tables.add(table)
    return tables

def get_operation_name(node):
    if isinstance(node, javalang.tree.MethodInvocation):
        return node.member
    if isinstance(node, javalang.tree.BinaryOperation):
        return f"binary_{node.operator}"
    return "assign"

def extract_relations_v17(project_path):
    nodes = {}
    relations = set()
    collection_entities = {}

    java_files = []
    # --- PASS 0: Folder/File ---
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        folder_name = rel_root if rel_root != "." else os.path.basename(project_path)
        folder_node = add_node(nodes, "folder", folder_name, root)

        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root,d), project_path)
            subfolder_node = add_node(nodes, "folder", subfolder_rel, os.path.join(root,d))
            add_relation(relations, folder_node, "contains", subfolder_node)

        for file in files:
            if file.endswith(".java"):
                file_rel = os.path.relpath(os.path.join(root,file), project_path)
                file_node = add_node(nodes, "file", file_rel, os.path.join(root,file))
                add_relation(relations, folder_node, "contains", file_node)
                java_files.append(os.path.join(root,file))

    # --- PASS 1: Parse Java Files ---
    parsed_files = {}
    for file_path in java_files:
        try:
            content = open(file_path,"r",encoding="utf-8").read()
            parsed_files[file_path] = (content, javalang.parse.parse(content))
        except:
            continue

    # --- PASS 2: Mongo Collections ---
    for file_path,(code,tree) in parsed_files.items():
        package_name = tree.package.name if tree.package else None
        for type_decl in tree.types:
            if isinstance(type_decl,javalang.tree.ClassDeclaration):
                full_class_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
                for ann in type_decl.annotations:
                    if ann.name == "Document":
                        for pair in ann.element:
                            if pair.name == "collection":
                                collection = pair.value.value
                                collection_entities[full_class_name] = collection
                                add_node(nodes,"class",full_class_name,file_path)
                                add_node(nodes,"collection",collection,file_path)
                                add_relation(relations,f"class:{full_class_name}","mapped_to_collection",f"collection:{collection}")

    # --- PASS 3: Classes / Methods / Variables ---
    for file_path,(code,tree) in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        file_node = f"file:{rel_path}"

        # APIs & DB
        for api in extract_api_calls(code):
            api_node = add_node(nodes,"api",api,file_path)
            add_relation(relations,file_node,"calls_api",api_node)

        for t in extract_db_table_usage_clean(code):
            table_node = add_node(nodes,"table",t,file_path)
            add_relation(relations,file_node,"uses_table",table_node)

        import_map = {imp.path.split('.')[-1]:imp.path for imp in tree.imports if imp.path and not imp.wildcard}
        package_name = tree.package.name if tree.package else None

        for type_decl in tree.types:
            if not isinstance(type_decl,(javalang.tree.ClassDeclaration,javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl,javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            class_node = add_node(nodes,decl_type,full_decl_name,file_path)
            add_relation(relations,file_node,"declares",class_node)

            for field in getattr(type_decl,"fields",[]):
                for decl in field.declarators:
                    var_full = f"{full_decl_name}.{decl.name}"
                    var_node = add_node(nodes,"variable",var_full,file_path,class_name=full_decl_name)
                    add_relation(relations,class_node,"has_variable",var_node)

            for method in getattr(type_decl,"methods",[]):
                method_full = f"{full_decl_name}.{method.name}"
                method_node = add_node(nodes,"method",method_full,file_path,class_name=full_decl_name)
                add_relation(relations,class_node,"has_method",method_node)

                if not method.body:
                    continue

                for path,node in method:
                    # Local variables
                    if isinstance(node,javalang.tree.LocalVariableDeclaration):
                        for decl in node.declarators:
                            var_full = f"{full_decl_name}.{method.name}.{decl.name}"
                            var_node = add_node(nodes,"variable",var_full,file_path,class_name=full_decl_name,method_name=method.name)
                            add_relation(relations,method_node,"uses",var_node)

                    # Assignment with transformations
                    elif isinstance(node,javalang.tree.Assignment):
                        left = getattr(node,"expressionl",None) or getattr(node,"left",None)
                        right = node.value
                        if isinstance(left,javalang.tree.MemberReference):
                            left_var = f"variable:{full_decl_name}.{method.name}.{left.member}"
                            add_relation(relations,method_node,"declares",left_var)

                            if isinstance(right,javalang.tree.MemberReference):
                                right_var = f"variable:{full_decl_name}.{method.name}.{right.member}"
                                add_relation(relations,right_var,"flows_to",left_var)

                            elif isinstance(right,javalang.tree.MethodInvocation):
                                op = get_operation_name(right)
                                src_var = f"variable:{full_decl_name}.{method.name}.{right.qualifier or 'tmp'}"
                                add_relation(relations,src_var,"transforms_to",left_var,op=op)

                            elif isinstance(right,javalang.tree.BinaryOperation):
                                op = get_operation_name(right)
                                for operand in (right.operandl,right.operandr):
                                    if isinstance(operand,javalang.tree.MemberReference):
                                        src_var = f"variable:{full_decl_name}.{method.name}.{operand.member}"
                                        add_relation(relations,src_var,"transforms_to",left_var,op=op)
    return nodes,relations


# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"

def push_to_neo4j(nodes, relations, uri=NEO4J_URI, user=NEO4J_USER, password=NEO4J_PASSWORD, db=NEO4J_DB):
    driver = GraphDatabase.driver(uri, auth=(user,password))
    with driver.session(database=db) as session:
        # Create nodes
        for node in nodes.values():
            session.run("""
                MERGE (n {id:$id})
                SET n.type=$type, n.name=$name, n.full_name=$full_name,
                    n.file_path=$file_path, n.class_name=$class_name, n.method_name=$method_name
            """, **node)

        # Create relations
        for src,rel,dst,op in relations:
            session.run(f"""
                MATCH (a {{id:$src}}), (b {{id:$dst}})
                MERGE (a)-[r:{rel}]->(b)
                SET r.op=$op
            """, {"src":src,"dst":dst,"op":op})
    driver.close()
    print("✅ Push to Neo4j Completed")


project_path = r"C:\Shaik\sample\OneInsights"

nodes, relations = extract_relations_v17(project_path)
save_graph_to_csv(nodes, relations, output_dir="graph_csv_v17")
push_to_neo4j(nodes, relations)
