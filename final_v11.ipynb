# ========== COMBINED DATA LINEAGE PIPELINE V11 ==========
# Combines complete pipeline from final_v9.ipynb with advanced features from final_v10.ipynb
# Base: final_v9.ipynb (complete working pipeline)
# Enhanced with: advanced prompts, memory optimizations, and variable context tracking from final_v10.ipynb

# STAGE 1: CONFIGURATION & INITIALIZATION
import os
import json
import re
import uuid
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time
from tenacity import retry, stop_after_attempt, wait_exponential

# Tree-sitter for AST parsing
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# LangChain components
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.schema import Document

# Configuration
BASE_PATH = Path(r"C:/Shaik/sample/ServicesBolt")

# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "servicesbolt"

# Initialize connections
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

# Azure OpenAI Configuration
llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

# Loop variables to avoid in transformation analysis only
LOOP_VARIABLES = {
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    'index', 'idx', 'counter', 'count'
}

# Enhanced comment removal function (from v10)
def remove_java_comments(source_code):
    """Remove all types of Java comments to avoid false positives in pattern matching"""
    # Remove single-line comments
    source_code = re.sub(r'//.*$', '', source_code, flags=re.MULTILINE)
    # Remove multi-line comments including /** */ blocks
    source_code = re.sub(r'/\*.*?\*/', '', source_code, flags=re.DOTALL)
    return source_code

# Long-term memory storage (enhanced from v10)
MEMORY_FILE = "servicesbolt_memory_v11.json"
memory_lock = threading.Lock()

def load_memory():
    """Load long-term memory from disk"""
    try:
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            # Convert lists back to sets where needed
            def convert_from_json(obj):
                if isinstance(obj, dict):
                    result = {}
                    for k, v in obj.items():
                        if k == 'validated_edges' and isinstance(v, list):
                            result[k] = set(v)
                        else:
                            result[k] = convert_from_json(v)
                    return result
                elif isinstance(obj, list):
                    return [convert_from_json(item) for item in obj]
                else:
                    return obj
            
            return convert_from_json(data)
            
    except FileNotFoundError:
        # Return enhanced default memory structure (from v10)
        return {
            'class_registry': {},
            'validated_edges': set(),
            'variable_flows': {},
            'method_signatures': {},
            'transformation_cache': {},
            'variable_contexts': {}  # Enhanced variable context tracking
        }
    except json.JSONDecodeError as e:
        print(f"⚠️ Error loading JSON memory file: {e}")
        return {
            'class_registry': {},
            'validated_edges': set(),
            'variable_flows': {},
            'method_signatures': {},
            'transformation_cache': {},
            'variable_contexts': {}  # Enhanced variable context tracking
        }

def save_memory(memory):
    """Save long-term memory to disk"""
    with memory_lock:
        try:
            memory_copy = memory.copy()
            
            # Convert sets to lists for JSON serialization
            def convert_for_json(obj):
                if isinstance(obj, set):
                    return list(obj)
                elif isinstance(obj, dict):
                    return {k: convert_for_json(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_for_json(item) for item in obj]
                else:
                    return obj
            
            memory_copy = convert_for_json(memory_copy)
            
            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(memory_copy, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Error saving memory to JSON: {e}")

# Initialize memory
memory = load_memory()

# Clear Neo4j database
graph.query("MATCH (n) DETACH DELETE n")
print("✅ Stage 1 Complete: Configuration loaded and Neo4j cleared")

# ========== RETRY LOGIC FOR RATE LIMITING (from v10) ==========
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=60))
def process_with_retry(transformer, doc):
    """Process LLM request with retry logic for rate limiting"""
    try:
        return transformer.convert_to_graph_documents([doc])
    except Exception as e:
        error_str = str(e).lower()
        if "429" in error_str or "rate limit" in error_str or "quota" in error_str:
            print(f"🔄 Rate limit hit, retrying after delay...")
            raise  # This will trigger the retry
        else:
            print(f"❌ Non-rate-limit error: {e}")
            raise  # Re-raise non-rate-limit errors

print("✅ Rate limiting retry logic loaded")

# ========== IMPROVED UTILITY FUNCTIONS (from v9 base) ==========

def to_pascal_case(text):
    """Convert text to PascalCase with improved handling"""
    if not text:
        return text
    
    # Remove file extensions first
    text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # Handle file paths - extract just the filename
    if '/' in text or '\\' in text:
        text = os.path.basename(text)
        text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # If already in PascalCase, return as is
    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):
        return text
    
    # Handle camelCase to PascalCase conversion
    if re.match(r'^[a-z][a-zA-Z0-9]*$', text):
        return text[0].upper() + text[1:]
    
    # Split on common delimiters and capitalize each part
    parts = re.split(r'[_\-\s]+', text)
    result = ''
    for part in parts:
        if part:
            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()
    
    return result if result else text

def extract_clean_name(full_name, name_type):
    """Extract clean name from potentially concatenated strings"""
    if not full_name:
        return full_name
    
    # Remove common prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if full_name.lower().startswith(prefix):
            full_name = full_name[len(prefix):]
    
    # Remove file extensions EXCEPT for file type
    if name_type.lower() != 'file':
        full_name = re.sub(r'\.(java|class)$', '', full_name, flags=re.IGNORECASE)
    
    # Handle file.class patterns - extract only class name
    if '.' in full_name and name_type.lower() in ['class', 'interface']:
        parts = full_name.split('.')
        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Handle classname:method or classname.method patterns
    if name_type.lower() == 'method':
        if ':' in full_name:
            parts = full_name.split(':')
            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
        elif '.' in full_name:
            parts = full_name.split('.')
            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Apply PascalCase for classes, methods, files, folders
    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:
        return to_pascal_case(full_name)
    
    # For variables, keep original name
    if name_type.lower() == 'variable':
        if '.' in full_name:
            return full_name.split('.')[-1]
        return full_name
    
    # For tables, apply PascalCase
    if name_type.lower() == 'table':
        return to_pascal_case(full_name)
    
    return full_name

def is_loop_variable(var_name):
    """Check if variable is a loop variable to avoid in transformations"""
    if not var_name:
        return True
    var_lower = var_name.lower().strip()
    return var_lower in LOOP_VARIABLES or len(var_lower) <= 1

def get_variable_context(var_name, method_name=None, class_name=None):
    """Get context for a variable (method or class where it's defined)"""
    if method_name:
        return extract_clean_name(method_name, 'method')
    elif class_name:
        return extract_clean_name(class_name, 'class')
    return None

print("✅ Improved utility functions loaded")

# ========== ENHANCED VARIABLE METADATA REGISTRY (from v10) ==========

class ImprovedVariableRegistry:
    """Enhanced registry to track variables with proper context separation"""
    
    def __init__(self):
        self.variables = {}  # var_id -> metadata
        self.name_to_id = {}  # (var_name, context) -> var_id
        self.chunk_memory = {}  # chunk_id -> variables seen
        
    def register_variable(self, var_name, context, chunk_id, context_info):
        """Register a variable with unique ID and context metadata"""
        # Clean variable name
        clean_var_name = extract_clean_name(var_name, 'variable')
        clean_context = extract_clean_name(context, context_info.get('context_type', 'method'))
        
        # Create unique key
        var_key = (clean_var_name, clean_context)
        
        if var_key in self.name_to_id:
            var_id = self.name_to_id[var_key]
            self.variables[var_id]['chunks'].add(chunk_id)
            self.variables[var_id]['contexts'].append(context_info)
        else:
            var_id = f"var_{uuid.uuid4().hex[:8]}"
            self.name_to_id[var_key] = var_id
            self.variables[var_id] = {
                'variable_name': clean_var_name,
                'context_name': clean_context,
                'context_type': context_info.get('context_type', 'method'),
                'chunks': {chunk_id},
                'contexts': [context_info],
                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,
                'modifications': [],
                'usages': [],
                'data_type': context_info.get('data_type'),
                'lineage_path': []
            }
        
        if chunk_id not in self.chunk_memory:
            self.chunk_memory[chunk_id] = set()
        self.chunk_memory[chunk_id].add(var_id)
        
        return var_id
    
    def get_variable_for_neo4j(self, var_id):
        """Get variable data formatted for Neo4j"""
        if var_id in self.variables:
            var_data = self.variables[var_id]
            return {
                'name': var_data['variable_name'],
                'context': var_data['context_name'],
                'context_type': var_data['context_type'],
                'full_context': f"{var_data['context_name']}.{var_data['variable_name']}"
            }
        return None

# Enhanced variable context tracking functions (from v10)
def create_unique_variable_name(var_name, context_name, context_type='method'):
    """Create unique variable name with context for proper relationship mapping"""
    if not var_name or not context_name:
        return var_name
    
    clean_var = extract_clean_name(var_name, 'variable')
    clean_context = extract_clean_name(context_name, context_type)
    
    return f"{clean_context}.{clean_var}"

def is_method_name(name, class_registry):
    """Check if a name is actually a method name to prevent method-variable confusion"""
    if not name:
        return False
    
    if name.endswith('()'):
        return True
    
    for class_name, class_info in class_registry.items():
        if 'source_code' in class_info:
            method_pattern = rf'\b(public|private|protected)\s+\w+\s+{re.escape(name)}\s*\('
            if re.search(method_pattern, class_info['source_code']):
                return True
    
    return False

# Initialize improved variable registry
variable_registry = ImprovedVariableRegistry()

print("✅ Enhanced Variable Registry initialized")

# ========== STAGE 2: IMPROVED FOLDER-FILE HIERARCHY ==========

def extract_folder_file_hierarchy():
    """Extract and normalize folder-file relationships with improved naming"""
    relationships = []
    base_folder = to_pascal_case(BASE_PATH.name)

    for root, dirs, files in os.walk(BASE_PATH):
        current_path = Path(root)
        rel_path = current_path.relative_to(BASE_PATH)

        # Determine current folder name and its parent
        if rel_path != Path('.'):
            folder_name = to_pascal_case(current_path.name)
            parent_rel_path = current_path.parent.relative_to(BASE_PATH)
            parent_name = base_folder if parent_rel_path == Path('.') else to_pascal_case(current_path.parent.name)

            relationships.append({
                'source_node': parent_name,
                'source_type': 'Folder',
                'destination_node': folder_name,
                'destination_type': 'Folder',
                'relationship': 'CONTAINS'
            })
            current_folder_name = folder_name
        else:
            current_folder_name = base_folder

        # Process files inside the folder
        for file in files:
            if file.lower().endswith(".java"):
                file_name = extract_clean_name(file, 'file')
                relationships.append({
                    'source_node': current_folder_name,
                    'source_type': 'Folder',
                    'destination_node': file_name,
                    'destination_type': 'File',
                    'relationship': 'CONTAINS',
                    'file_path': str(current_path / file)
                })

    return relationships

# Execute Stage 2
folder_file_relationships = extract_folder_file_hierarchy()
df_hierarchy = pd.DataFrame(folder_file_relationships)

# Store Stage 2 results in memory
memory['stage_2_results'] = {
    'relationships': len(df_hierarchy),
    'folders': len([r for r in folder_file_relationships if r['destination_type'] == 'Folder']),
    'files': len([r for r in folder_file_relationships if r['destination_type'] == 'File'])
}

# Add validated edges to prevent duplicates
for _, row in df_hierarchy.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
save_memory(memory)

print(f"✅ Stage 2 Complete: {len(df_hierarchy)} folder/file relationships extracted")
print(f"📁 Folders: {memory['stage_2_results']['folders']}, Files: {memory['stage_2_results']['files']}")

# ========== STAGE 2B: AST-BASED FILE-CLASS RELATIONSHIPS ==========

def extract_file_class_relationships():
    """Extract file-class relationships using AST parsing"""
    relationships = []
    
    for root, dirs, files in os.walk(BASE_PATH):
        for file in files:
            if file.lower().endswith(".java"):
                file_path = Path(root) / file
                file_name = extract_clean_name(file, 'file')
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Parse with tree-sitter
                    tree = parser.parse(bytes(content, 'utf8'))
                    
                    # Extract classes and interfaces
                    def extract_classes_from_node(node):
                        classes = []
                        if node.type in ['class_declaration', 'interface_declaration']:
                            name_node = None
                            for child in node.children:
                                if child.type == 'identifier':
                                    name_node = child
                                    break
                            if name_node:
                                class_name = content[name_node.start_byte:name_node.end_byte]
                                class_type = 'Class' if node.type == 'class_declaration' else 'Interface'
                                classes.append((to_pascal_case(class_name), class_type))
                        
                        for child in node.children:
                            classes.extend(extract_classes_from_node(child))
                        
                        return classes
                    
                    classes = extract_classes_from_node(tree.root_node)
                    
                    for class_name, class_type in classes:
                        relationships.append({
                            'source_node': file_name,
                            'source_type': 'File',
                            'destination_node': class_name,
                            'destination_type': class_type,
                            'relationship': 'DECLARES',
                            'file_path': str(file_path)
                        })
                        
                except Exception as e:
                    print(f"⚠️ Error processing {file_path}: {e}")
                    continue
    
    return relationships

# Execute Stage 2B
file_class_relationships = extract_file_class_relationships()
df_file_class = pd.DataFrame(file_class_relationships)

# Store Stage 2B results in memory
memory['stage_2b_results'] = {
    'relationships': len(df_file_class),
    'classes': len([r for r in file_class_relationships if r['destination_type'] == 'Class']),
    'interfaces': len([r for r in file_class_relationships if r['destination_type'] == 'Interface'])
}

# Add validated edges
for _, row in df_file_class.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
save_memory(memory)

print(f"✅ Stage 2B Complete: {len(df_file_class)} file-class relationships extracted")
print(f"🏗️ Classes: {memory['stage_2b_results']['classes']}, Interfaces: {memory['stage_2b_results']['interfaces']}")

# ========== STAGE 3: ENHANCED CLASS REGISTRY & ANALYSIS ==========

def build_enhanced_class_registry():
    """Build comprehensive class registry with enhanced metadata"""
    class_registry = {}
    
    for root, dirs, files in os.walk(BASE_PATH):
        for file in files:
            if file.lower().endswith(".java"):
                file_path = Path(root) / file
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Remove comments to avoid false positives (enhanced from v10)
                    clean_content = remove_java_comments(content)
                    
                    # Parse with tree-sitter
                    tree = parser.parse(bytes(content, 'utf8'))
                    
                    def extract_class_info(node, content):
                        classes = []
                        if node.type in ['class_declaration', 'interface_declaration']:
                            class_info = {
                                'type': 'class' if node.type == 'class_declaration' else 'interface',
                                'file_path': str(file_path),
                                'source_code': content[node.start_byte:node.end_byte],
                                'methods': [],
                                'fields': [],
                                'annotations': [],
                                'extends': None,
                                'implements': []
                            }
                            
                            # Extract class name
                            for child in node.children:
                                if child.type == 'identifier':
                                    class_name = content[child.start_byte:child.end_byte]
                                    class_info['name'] = to_pascal_case(class_name)
                                    break
                            
                            # Extract methods, fields, annotations
                            for child in node.children:
                                if child.type == 'class_body':
                                    for body_child in child.children:
                                        if body_child.type == 'method_declaration':
                                            method_name = None
                                            for method_child in body_child.children:
                                                if method_child.type == 'identifier':
                                                    method_name = content[method_child.start_byte:method_child.end_byte]
                                                    break
                                            if method_name:
                                                class_info['methods'].append(to_pascal_case(method_name))
                                        
                                        elif body_child.type == 'field_declaration':
                                            for field_child in body_child.children:
                                                if field_child.type == 'variable_declarator':
                                                    for var_child in field_child.children:
                                                        if var_child.type == 'identifier':
                                                            field_name = content[var_child.start_byte:var_child.end_byte]
                                                            class_info['fields'].append(field_name)
                                                            break
                            
                            # Detect annotations (enhanced from v10)
                            annotation_patterns = [
                                r'@RestController', r'@Controller', r'@Service', r'@Repository',
                                r'@Entity', r'@Table', r'@Component', r'@Configuration'
                            ]
                            for pattern in annotation_patterns:
                                if re.search(pattern, class_info['source_code']):
                                    class_info['annotations'].append(pattern[1:])  # Remove @
                            
                            if 'name' in class_info:
                                classes.append((class_info['name'], class_info))
                        
                        for child in node.children:
                            classes.extend(extract_class_info(child, content))
                        
                        return classes
                    
                    classes = extract_class_info(tree.root_node, content)
                    
                    for class_name, class_info in classes:
                        class_registry[class_name] = class_info
                        
                except Exception as e:
                    print(f"⚠️ Error processing {file_path}: {e}")
                    continue
    
    return class_registry

# Execute Stage 3
class_registry = build_enhanced_class_registry()
memory['class_registry'] = class_registry

# Store Stage 3 results
memory['stage_3_results'] = {
    'total_classes': len(class_registry),
    'controllers': len([c for c in class_registry.values() if 'RestController' in c.get('annotations', []) or 'Controller' in c.get('annotations', [])]),
    'services': len([c for c in class_registry.values() if 'Service' in c.get('annotations', [])]),
    'entities': len([c for c in class_registry.values() if 'Entity' in c.get('annotations', [])]),
    'repositories': len([c for c in class_registry.values() if 'Repository' in c.get('annotations', [])])
}

save_memory(memory)

print(f"✅ Stage 3 Complete: {len(class_registry)} classes registered")
print(f"🎯 Controllers: {memory['stage_3_results']['controllers']}, Services: {memory['stage_3_results']['services']}")
print(f"📊 Entities: {memory['stage_3_results']['entities']}, Repositories: {memory['stage_3_results']['repositories']}")

# ========== STAGE 3B: COMPREHENSIVE AST EXTRACTION ==========

def extract_comprehensive_ast_data():
    """Extract comprehensive AST data for all Java files"""
    ast_data = []
    
    for root, dirs, files in os.walk(BASE_PATH):
        for file in files:
            if file.lower().endswith(".java"):
                file_path = Path(root) / file
                file_name = extract_clean_name(file, 'file')
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Parse with tree-sitter
                    tree = parser.parse(bytes(content, 'utf8'))
                    
                    def extract_ast_relationships(node, parent_name=None, parent_type=None):
                        relationships = []
                        
                        if node.type in ['class_declaration', 'interface_declaration']:
                            # Extract class/interface name
                            class_name = None
                            for child in node.children:
                                if child.type == 'identifier':
                                    class_name = to_pascal_case(content[child.start_byte:child.end_byte])
                                    break
                            
                            if class_name:
                                # File declares class/interface
                                relationships.append({
                                    'source_node': file_name,
                                    'source_type': 'File',
                                    'destination_node': class_name,
                                    'destination_type': 'Class' if node.type == 'class_declaration' else 'Interface',
                                    'relationship': 'DECLARES',
                                    'file_path': str(file_path)
                                })
                                
                                # Process class body
                                for child in node.children:
                                    if child.type == 'class_body':
                                        relationships.extend(extract_ast_relationships(child, class_name, 'Class'))
                        
                        elif node.type == 'method_declaration' and parent_name:
                            # Extract method name
                            method_name = None
                            for child in node.children:
                                if child.type == 'identifier':
                                    method_name = to_pascal_case(content[child.start_byte:child.end_byte])
                                    break
                            
                            if method_name:
                                # Class contains method
                                relationships.append({
                                    'source_node': parent_name,
                                    'source_type': parent_type,
                                    'destination_node': method_name,
                                    'destination_type': 'Method',
                                    'relationship': 'CONTAINS',
                                    'file_path': str(file_path)
                                })
                                
                                # Process method body for variables
                                for child in node.children:
                                    if child.type == 'block':
                                        relationships.extend(extract_ast_relationships(child, method_name, 'Method'))
                        
                        elif node.type == 'field_declaration' and parent_name:
                            # Extract field variables
                            for child in node.children:
                                if child.type == 'variable_declarator':
                                    for var_child in child.children:
                                        if var_child.type == 'identifier':
                                            var_name = content[var_child.start_byte:var_child.end_byte]
                                            # Keep all variables for basic structure extraction
                                            relationships.append({
                                                    'source_node': parent_name,
                                                    'source_type': parent_type,
                                                    'destination_node': var_name,
                                                    'destination_type': 'Variable',
                                                    'relationship': 'HAS_FIELD',
                                                    'file_path': str(file_path)
                                                })
                        
                        elif node.type == 'local_variable_declaration' and parent_name:
                            # Extract local variables
                            for child in node.children:
                                if child.type == 'variable_declarator':
                                    for var_child in child.children:
                                        if var_child.type == 'identifier':
                                            var_name = content[var_child.start_byte:var_child.end_byte]
                                            # Keep all variables for basic structure extraction
                                            relationships.append({
                                                    'source_node': parent_name,
                                                    'source_type': parent_type,
                                                    'destination_node': var_name,
                                                    'destination_type': 'Variable',
                                                    'relationship': 'DECLARES',
                                                    'file_path': str(file_path)
                                                })
                        
                        # Recursively process children
                        for child in node.children:
                            relationships.extend(extract_ast_relationships(child, parent_name, parent_type))
                        
                        return relationships
                    
                    file_relationships = extract_ast_relationships(tree.root_node)
                    ast_data.extend(file_relationships)
                    
                except Exception as e:
                    print(f"⚠️ Error processing AST for {file_path}: {e}")
                    continue
    
    return ast_data

# Execute Stage 3B
ast_relationships = extract_comprehensive_ast_data()
df_ast = pd.DataFrame(ast_relationships)

# Store Stage 3B results
memory['stage_3b_results'] = {
    'total_relationships': len(df_ast),
    'file_class': len([r for r in ast_relationships if r['relationship'] == 'DECLARES' and r['source_type'] == 'File']),
    'class_method': len([r for r in ast_relationships if r['relationship'] == 'CONTAINS' and r['destination_type'] == 'Method']),
    'class_field': len([r for r in ast_relationships if r['relationship'] == 'HAS_FIELD']),
    'method_variable': len([r for r in ast_relationships if r['relationship'] == 'DECLARES' and r['source_type'] == 'Method'])
}

# Add validated edges
for _, row in df_ast.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)

save_memory(memory)

print(f"✅ Stage 3B Complete: {len(df_ast)} AST relationships extracted")
print(f"🔗 File→Class: {memory['stage_3b_results']['file_class']}, Class→Method: {memory['stage_3b_results']['class_method']}")
print(f"📝 Class→Field: {memory['stage_3b_results']['class_field']}, Method→Variable: {memory['stage_3b_results']['method_variable']}")

# ========== ENHANCED PROMPTS FROM V10 ==========

def escape_braces_for_langchain(text):
    """Escape curly braces in Java code to prevent LangChain template variable conflicts"""
    if not text:
        return text
    return text.replace('{', '{{').replace('}', '}}')

def build_consolidated_stage4b_prompt(file_path, ast_df, class_registry):
    """Build consolidated Stage 4B prompt for file-class-method-variable relationships (from v10)"""
    
    # Extract file name from path
    file_name = to_pascal_case(Path(file_path).stem) if file_path else 'UnknownFile'
    
    # Get class registry info for this file
    file_classes = [name for name, info in class_registry.items() 
                   if info.get('file_path') == file_path]
    
    # Build endpoint context
    endpoint_context = ''
    controllers = [c for c in file_classes if 'RestController' in class_registry[c].get('annotations', [])]
    if controllers:
        endpoint_context = f"\nREST Controllers in {file_name}: {', '.join(controllers)}"
    
    # Build database context
    db_entities = [c for c in file_classes if 'Entity' in class_registry[c].get('annotations', [])]
    db_context = ''
    if db_entities:
        db_list = [f"- {entity} (Entity)" for entity in db_entities]
        db_context = f"\nDatabase Entities in {file_name}:\n" + "\n".join(db_list)
    
    # Build structural-only prompt (Stage 4B focuses on code structure)
    prompt = f"""
You are a Java code structure extraction engine. Extract ONLY structural relationships from this Java file.

CURRENT FILE: {file_name}
{endpoint_context}
{db_context}

EXTRACT STRUCTURAL RELATIONSHIPS ONLY:

1. FILE-CLASS RELATIONSHIPS:
   - File -[DECLARES]-> Class (detect all classes/interfaces in file)
   - File -[DECLARES]-> Interface (detect all interfaces in file)

2. CLASS-METHOD RELATIONSHIPS:
   - Class -[CONTAINS]-> Method (all methods in classes)
   - Interface -[CONTAINS]-> Method (all methods in interfaces)

3. CLASS-VARIABLE RELATIONSHIPS:
   - Class -[HAS_FIELD]-> Variable (class-level fields only)
   - Method -[DECLARES]-> Variable (method parameters and local variables)

4. INHERITANCE & IMPLEMENTATION:
   - Class -[EXTENDS]-> Class (class inheritance)
   - Class -[IMPLEMENTS]-> Interface (interface implementation)
   - Method -[CALLS]-> Method (method invocations)

5. BUSINESS RELATIONSHIPS:
   - Class -[DECLARES]-> Endpoint (for @RestController classes)
   - Class -[MAPS_TO]-> Table (for @Entity classes)

NOTE: Data flow relationships (PRODUCES, FLOWS_TO, TRANSFORMS_TO, USES) are handled in Stage 5B.

3. NAMING RULES:
   - Use PascalCase for Files, Classes, Methods (e.g., DateUtil, ConvertDtoToEntity)
   - Use original names for Variables (e.g., orderDto, entityList)
   - Remove prefixes like 'method:', 'class:', 'variable:'
   - For endpoints: use descriptive names like 'GetOrdersEndpoint'
   - For tables: use PascalCase table names

4. VARIABLE FILTERING:
   - EXCLUDE only loop variables: i, j, k, l, m, n, x, y, z, index, idx, counter, count
   - INCLUDE all meaningful variables: DTOs, entities, service calls, business data, temp variables with business meaning

5. CONTEXT TRACKING:
   - Global variables (class fields): context = class name
   - Local variables (method variables): context = method name

Extract relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the relationship triples, no explanations.
"""
    return prompt.replace("{", "{{").replace("}", "}}")

def smart_chunk_strategy(file_path, content):
    """Smart chunking: whole file if <1000 lines, language chunks if larger"""
    lines = content.count('\n') + 1
    escaped_content = escape_braces_for_langchain(content)
    
    if lines <= 1000:
        # Small file: process as single chunk
        return [Document(
            page_content=escaped_content,
            metadata={'source': str(file_path), 'chunk_type': 'whole_file'}
        )]
    else:
        # Large file: use language-aware chunking
        text_splitter = RecursiveCharacterTextSplitter.from_language(
            language=LC_Language.JAVA,
            chunk_size=2000,
            chunk_overlap=200
        )
        
        docs = text_splitter.create_documents(
            [escaped_content],
            metadatas=[{'source': str(file_path), 'chunk_type': 'language_chunk'}]
        )
        
        return docs

def build_first_llm_structure_prompt(file_path, ast_context):
    """Build prompt for first LLM: basic structure extraction from file chunks + AST"""
    
    file_name = to_pascal_case(Path(file_path).stem) if file_path else 'UnknownFile'
    
    prompt = f"""
You are a Java code structure extraction engine. Extract ONLY basic structural relationships from this Java file chunk.

CURRENT FILE: {file_name}

AST CONTEXT PROVIDED:
{ast_context}

EXTRACT BASIC STRUCTURAL RELATIONSHIPS ONLY:

1. FILE-CLASS RELATIONSHIPS:
   - File -[DECLARES]-> Class (detect all classes in file)
   - File -[DECLARES]-> Interface (detect all interfaces in file)

2. CLASS-METHOD RELATIONSHIPS:
   - Class -[CONTAINS]-> Method (all methods in classes)
   - Interface -[CONTAINS]-> Method (all methods in interfaces)

3. VARIABLE DECLARATIONS:
   - Class -[HAS_FIELD]-> Variable (class-level fields)
   - Method -[DECLARES]-> Variable (method parameters and local variables)

4. BASIC INHERITANCE:
   - Class -[EXTENDS]-> Class (class inheritance)
   - Class -[IMPLEMENTS]-> Interface (interface implementation)

NAMING RULES:
- Use PascalCase for Files, Classes, Methods
- Use original names for Variables
- Include ALL variables (we'll filter later)

Extract relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the relationship triples, no explanations.
"""
    return prompt.replace("{", "{{").replace("}", "}}")

def build_second_llm_transformation_prompt(class_info, variable_flows, variable_contexts, transformation_cache):
    """Build prompt for second LLM: end-to-end variable transformations"""
    
    class_name = class_info.get('name', 'UnknownClass')
    
    # Build context from variable flows and contexts
    context_info = f"Class: {class_name}\n\n"
    
    if variable_flows:
        context_info += "Variable Flows:\n"
        for var, flows in variable_flows.items():
            if class_name in var or any(class_name in flow for flow in flows):
                context_info += f"- {var}: {flows}\n"
    
    if variable_contexts:
        context_info += "\nVariable Contexts:\n"
        for var, ctx in variable_contexts.items():
            if ctx.get('context') == class_name:
                context_info += f"- {var}: {ctx.get('context_type', 'unknown')} in {ctx.get('context')}\n"
    
    prompt = f"""
You are an advanced Java variable transformation analysis engine. Analyze end-to-end variable transformations and method calls.

{context_info}

EXTRACT END-TO-END TRANSFORMATION RELATIONSHIPS:

1. METHOD VARIABLE PRODUCTION:
   - Method -[PRODUCES]-> Variable (when method call assigns to variable)
   - Example: service.getData() assigns to 'result' → getData -[PRODUCES]-> result

2. VARIABLE TRANSFORMATIONS:
   - Variable -[TRANSFORMS_TO]-> Variable (data type conversions, processing)
   - Example: dto.toEntity() → dto -[TRANSFORMS_TO]-> entity

3. VARIABLE FLOWS:
   - Variable -[FLOWS_TO]-> Method (variable passed to method)
   - Variable -[FLOWS_TO]-> Variable (assignment, parameter passing)

4. METHOD CALL CHAINS:
   - Method -[CALLS]-> Method (method invocations)
   - Variable -[RETURNS_DATA_TO]-> Variable (return value assignments)

5. COLLECTION OPERATIONS:
   - Variable -[FLOWS_TO]-> CollectionEntry (list.add, map.put operations)

VARIABLE FILTERING:
- EXCLUDE only loop variables: i, j, k, l, m, n, x, y, z, index, idx, counter, count
- INCLUDE all business variables: DTOs, entities, services, temp variables with meaning

FOCUS ON:
- How variables transform through method calls
- End-to-end data flow from input to output
- Business logic transformations

Extract relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the relationship triples, no explanations.
"""
    return prompt.replace("{", "{{").replace("}", "}}")

print("✅ Enhanced prompts and chunking strategy loaded from v10")

# ========== STAGE 4: FIRST LLM - BASIC STRUCTURE EXTRACTION ==========

def run_first_llm_structure_extraction():
    """First LLM: Extract basic structure from file chunks + AST data"""
    
    # Prepare documents with AST context for processing
    smart_docs = []
    
    for root, dirs, files in os.walk(BASE_PATH):
        for file in files:
            if file.lower().endswith(".java"):
                file_path = Path(root) / file
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Apply smart chunking strategy
                    # Build AST context for this file
                    ast_context = "AST Relationships for this file:\n"
                    if not df_ast.empty:
                        file_ast = df_ast[df_ast['file_path'] == str(file_path)]
                        for _, row in file_ast.iterrows():
                            ast_context += f"- {row['source_node']} -[{row['relationship']}]-> {row['destination_node']}\n"
                    
                    file_docs = smart_chunk_strategy(file_path, content)
                    
                    # Add AST context to each document
                    for doc in file_docs:
                        doc.metadata['ast_context'] = ast_context
                    
                    smart_docs.extend(file_docs)
                    
                except Exception as e:
                    print(f"⚠️ Error reading {file_path}: {e}")
                    continue
    
    print(f"📄 Prepared {len(smart_docs)} smart document chunks with AST context")
    
    # Process with first LLM for basic structure extraction
    all_basic_relationships = []
    
    for doc_info in tqdm(smart_docs, desc='🤖 First LLM: Basic Structure Extraction'):
        file_path = doc_info.metadata.get('source')
        
        ast_context = doc_info.metadata.get('ast_context', '')
        
        # Use first LLM prompt for basic structure extraction
        structure_prompt = build_first_llm_structure_prompt(file_path, ast_context)
        
        # Configure transformer with all allowed relationships
        transformer = LLMGraphTransformer(
            llm=llm,
            additional_instructions=structure_prompt,
            allowed_nodes=['File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint'],
            allowed_relationships=[
                # Structural relationships only - Stage 4B focuses on code structure
                ('File', 'DECLARES', 'Class'),
                ('File', 'DECLARES', 'Interface'),
                ('Class', 'CONTAINS', 'Method'),
                ('Interface', 'CONTAINS', 'Method'),
                ('Class', 'HAS_FIELD', 'Variable'),
                ('Method', 'DECLARES', 'Variable'),
                ('Class', 'EXTENDS', 'Class'),
                ('Class', 'IMPLEMENTS', 'Interface'),
                ('Method', 'CALLS', 'Method'),
                ('Class', 'DECLARES', 'Endpoint'),
                ('Class', 'MAPS_TO', 'Table')
            ],
            strict_mode=False,
            node_properties=False,
            relationship_properties=False
        )
        
        try:
            # Use retry logic for rate limiting (from v10)
            graph_docs = process_with_retry(transformer, doc_info)
            
            if graph_docs:
                for graph_doc in graph_docs:
                    # Extract relationships with enhanced validation
                    for relationship in graph_doc.relationships:
                        source_name = extract_clean_name(relationship.source.id, relationship.source.type)
                        dest_name = extract_clean_name(relationship.target.id, relationship.target.type)
                        
                        # Enhanced filtering (from v10)
                        # Keep all variables for basic structure extraction (1st LLM stage)
                        # Prevent method-variable confusion
                        if relationship.target.type == 'Variable' and is_method_name(dest_name, class_registry):
                            continue
                        
                        # Create edge key for deduplication
                        edge_key = f"{source_name}-{relationship.type}-{dest_name}"
                        
                        if edge_key not in memory['validated_edges']:
                            all_basic_relationships.append({
                                'source_node': source_name,
                                'source_type': relationship.source.type,
                                'destination_node': dest_name,
                                'destination_type': relationship.target.type,
                                'relationship': relationship.type,
                                'file_path': file_path
                            })
                            memory['validated_edges'].add(edge_key)
                            
        except Exception as e:
            print(f"⚠️ Error processing {file_path}: {e}")
            continue
    
    print(f"✅ First LLM: Extracted {len(all_basic_relationships)} basic structural relationships")
    return pd.DataFrame(all_basic_relationships)\n",
    "\n",
    "def run_second_llm_transformation_analysis():
    """Second LLM: Analyze end-to-end variable transformations using class registry and memory"""
    
    all_transformation_relationships = []
    
    # Process classes in batches
    class_items = list(class_registry.items())
    batch_size = 5  # Process 5 classes at a time
    
    for i in tqdm(range(0, len(class_items), batch_size), desc='🤖 Second LLM: Transformation Analysis'):
        batch = class_items[i:i+batch_size]
        
        for class_name, class_info in batch:
            # Build transformation prompt with memory context
            transformation_prompt = build_second_llm_transformation_prompt(
                {'name': class_name, **class_info},
                memory.get('variable_flows', {}),
                memory.get('variable_contexts', {}),
                memory.get('transformation_cache', {})
            )
            
            # Configure transformer for transformation analysis
            transformer = LLMGraphTransformer(
                llm=llm,
                additional_instructions=transformation_prompt,
                allowed_nodes=['Method', 'Variable', 'CollectionEntry'],
                allowed_relationships=[
                    ('Method', 'PRODUCES', 'Variable'),
                    ('Variable', 'TRANSFORMS_TO', 'Variable'),
                    ('Variable', 'FLOWS_TO', 'Method'),
                    ('Variable', 'FLOWS_TO', 'Variable'),
                    ('Method', 'CALLS', 'Method'),
                    ('Variable', 'RETURNS_DATA_TO', 'Variable'),
                    ('Variable', 'FLOWS_TO', 'CollectionEntry')
                ]
            )
            
            # Create a document with class context
            class_context = f"Class: {class_name}\nMethods: {class_info.get('methods', [])}\nFields: {class_info.get('fields', [])}"
            doc = Document(page_content=class_context, metadata={'class_name': class_name})
            
            try:
                # Process with retry logic
                graph_doc = process_with_retry(transformer, [doc])
                
                if graph_doc and graph_doc.relationships:
                    for relationship in graph_doc.relationships:
                        source_name = extract_clean_name(relationship.source.id, relationship.source.type)
                        dest_name = extract_clean_name(relationship.target.id, relationship.target.type)
                        
                        # Filter only loop variables in transformation analysis
                        if relationship.source.type == 'Variable' and is_loop_variable(source_name):
                            continue
                        if relationship.target.type == 'Variable' and is_loop_variable(dest_name):
                            continue
                        
                        # Create edge key for deduplication
                        edge_key = f"{source_name}-{relationship.type}-{dest_name}"
                        
                        if edge_key not in memory['validated_edges']:
                            all_transformation_relationships.append({
                                'source_node': source_name,
                                'source_type': relationship.source.type,
                                'destination_node': dest_name,
                                'destination_type': relationship.target.type,
                                'relationship': relationship.type,
                                'class_context': class_name
                            })
                            memory['validated_edges'].add(edge_key)
                            
            except Exception as e:
                print(f"⚠️ Error processing class {class_name}: {e}")
                continue
    
    print(f"✅ Second LLM: Extracted {len(all_transformation_relationships)} transformation relationships")
    return pd.DataFrame(all_transformation_relationships)


# Execute Two-Stage LLM Processing\n
print("🚀 Starting Two-Stage LLM Processing...")
# Stage 1: Basic Structure Extraction\n
df_basic_structure = run_first_llm_structure_extraction()

# Stage 2: Transformation Analysis\n
df_transformations = run_second_llm_transformation_analysis()
# Combine results\n
df_llm_lineage = pd.concat([df_basic_structure, df_transformations], ignore_index=True)
# Store Two-Stage LLM results\n
memory['two_stage_llm_results'] = {
    'basic_structure_count': len(df_basic_structure),
    'transformation_count': len(df_transformations),
    'total_relationships': len(df_llm_lineage),
    'relationship_types': df_llm_lineage['relationship'].value_counts().to_dict()
}
save_memory(memory)
print(f"📊 Two-Stage LLM Results:")
print(f"   - Basic Structure: {len(df_basic_structure)} relationships")
print(f"   - Transformations: {len(df_transformations)} relationships")
print(f"   - Total: {len(df_llm_lineage)} relationships")
print(f"📈 Relationship breakdown: {memory['two_stage_llm_results']['relationship_types']}")
print("✅ Two-Stage LLM Processing completed successfully!")

# ========== STAGE 5B: ADVANCED TRANSFORMATION ANALYSIS (from v10) ==========

def run_advanced_transformation_analysis():
    """Run advanced transformation analysis with enhanced prompts from v10"""
    
    if not class_registry:
        print("❌ No class registry found. Run previous stages first.")
        return pd.DataFrame()
    
    print(f"📊 Processing {len(class_registry)} classes for advanced transformations")
    
    # Enhanced LLM prompt for variable transformations (from v10)
    enhanced_prompt = """
You are an advanced Java data flow analysis engine specializing in variable transformations and method-level data flows.

CRITICAL TRANSFORMATION PATTERNS TO CAPTURE:

1. METHOD VARIABLE PRODUCTION:
   - When a variable is assigned from a method call: Method -[PRODUCES]-> Variable
   - Example: String result = service.processData(input) → processData -[PRODUCES]-> result

2. COLLECTION OPERATIONS:
   - When collections are updated: Variable -[FLOWS_TO]-> CollectionEntry
   - map.put(key, value) → value -[FLOWS_TO]-> CollectionEntry:map.key
   - list.add(item) → item -[FLOWS_TO]-> CollectionEntry:list

3. VARIABLE TRANSFORMATIONS INSIDE METHODS:
   - Data type conversions: Variable -[TRANSFORMS_TO]-> Variable
   - String operations: originalStr -[TRANSFORMS_TO]-> processedStr
   - Object mapping: dto -[TRANSFORMS_TO]-> entity

4. COMPLEX FLOWS:
   - Method chaining: var1 -[FLOWS_TO]-> method1 -[PRODUCES]-> var2 -[FLOWS_TO]-> method2
   - Conditional assignments: condition ? var1 : var2 -[FLOWS_TO]-> result
   - Loop variable updates: loopVar -[TRANSFORMS_TO]-> updatedLoopVar

5. RETURN VALUE FLOWS:
   - Method return values: Variable -[RETURNS_DATA_TO]-> Variable
   - Return transformations: processedData -[RETURNS_DATA_TO]-> methodResult

FOCUS ON CAPTURING TRANSFORMATIONS THAT OCCUR INSIDE METHOD BODIES, NOT JUST METHOD SIGNATURES.

Output format: source_node|relationship|destination_node|source_type|destination_type
"""
    
    all_relationships = []
    
    # Process classes in batches for memory efficiency (from v10)
    class_names = list(class_registry.keys())
    batch_size = 5  # Smaller batches for detailed analysis
    
    for i in range(0, len(class_names), batch_size):
        batch_classes = class_names[i:i+batch_size]
        print(f"🔄 Processing batch {i//batch_size + 1}/{(len(class_names) + batch_size - 1)//batch_size}")
        
        for class_name in batch_classes:
            class_info = class_registry[class_name]
            
            # Build enhanced context using variable registry (from v10)
            context_info = f"""
Class: {class_name}
File: {class_info.get('file_path', 'Unknown')}

Variable Context from Registry:
"""
            
            # Add variable context information
            variable_contexts = memory.get('variable_contexts', {})
            for var_name, var_info in variable_contexts.items():
                if var_info.get('context') == class_name or class_name in var_name:
                    context_info += f"- {var_name}: {var_info.get('context_type', 'unknown')} scope in {var_info.get('context', 'unknown')}\n"
            
            # Add method signatures for better understanding
            method_sigs = memory.get('method_signatures', {})
            if class_name in method_sigs:
                context_info += f"\nMethod Signatures:\n"
                for method, sig in method_sigs[class_name].items():
                    context_info += f"- {method}: {sig}\n"
            
            # Add source code for analysis
            source_code = class_info.get('source_code', '')
            if source_code:
                context_info += f"\nSource Code:\n{source_code[:2000]}..."  # Limit for token efficiency
            
            try:
                # Create document for LLM processing
                doc = Document(
                    page_content=f"Analyze this Java class for variable transformations:\n\n{context_info}",
                    metadata={'class_name': class_name}
                )
                
                # Configure transformer for transformation relationships
                transformer = LLMGraphTransformer(
                    llm=llm,
                    additional_instructions=enhanced_prompt,
                    allowed_nodes=['Method', 'Variable', 'CollectionEntry'],
                    allowed_relationships=[
                        ('Method', 'PRODUCES', 'Variable'),
                        ('Variable', 'FLOWS_TO', 'CollectionEntry'),
                        ('Variable', 'TRANSFORMS_TO', 'Variable'),
                        ('Variable', 'FLOWS_TO', 'Method'),
                        ('Variable', 'RETURNS_DATA_TO', 'Variable')
                    ],
                    strict_mode=False,
                    node_properties=False,
                    relationship_properties=False
                )
                
                # Process with retry logic
                graph_docs = process_with_retry(transformer, doc)
                
                if graph_docs:
                    for graph_doc in graph_docs:
                        for relationship in graph_doc.relationships:
                            source_name = extract_clean_name(relationship.source.id, relationship.source.type)
                            dest_name = extract_clean_name(relationship.target.id, relationship.target.type)
                            
                            # Filter only loop variables in transformation analysis
                            if relationship.source.type == 'Variable' and is_loop_variable(source_name):
                                continue
                            if relationship.target.type == 'Variable' and is_loop_variable(dest_name):
                                continue
                            
                            edge_key = f"{source_name}-{relationship.type}-{dest_name}"
                            
                            if edge_key not in memory['validated_edges']:
                                all_relationships.append({
                                    'source_node': source_name,
                                    'source_type': relationship.source.type,
                                    'destination_node': dest_name,
                                    'destination_type': relationship.target.type,
                                    'relationship': relationship.type,
                                    'class_context': class_name
                                })
                                memory['validated_edges'].add(edge_key)
                                
            except Exception as e:
                print(f"⚠️ Error processing transformations for {class_name}: {e}")
                continue
    
    return pd.DataFrame(all_relationships)

# Execute Stage 5B: Advanced Transformation Analysis
print("🚀 Starting Stage 5B: Advanced Transformation Analysis...")
df_transformations = run_advanced_transformation_analysis()

# Store Stage 5B results
memory['stage_5b_results'] = {
    'total_transformations': len(df_transformations),
    'produces': len(df_transformations[df_transformations['relationship'] == 'PRODUCES']),
    'transforms_to': len(df_transformations[df_transformations['relationship'] == 'TRANSFORMS_TO']),
    'flows_to': len(df_transformations[df_transformations['relationship'] == 'FLOWS_TO']),
    'returns_data_to': len(df_transformations[df_transformations['relationship'] == 'RETURNS_DATA_TO'])
}

save_memory(memory)

print(f"✅ Stage 5B Complete: {len(df_transformations)} transformation relationships extracted")
print(f"🏭 Produces: {memory['stage_5b_results']['produces']}, Transforms: {memory['stage_5b_results']['transforms_to']}")
print(f"🌊 Flows: {memory['stage_5b_results']['flows_to']}, Returns: {memory['stage_5b_results']['returns_data_to']}")

# ========== STAGE 6: CONSOLIDATION AND NEO4J UPLOAD ==========

def consolidate_and_upload_to_neo4j():
    """Consolidate all relationships and upload to Neo4j with enhanced validation"""
    
    # Combine all relationship DataFrames
    all_dataframes = []
    
    if not df_hierarchy.empty:
        all_dataframes.append(df_hierarchy[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])
        print(f"📁 Added {len(df_hierarchy)} folder/file relationships")
    
    if not df_file_class.empty:
        all_dataframes.append(df_file_class[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])
        print(f"🏗️ Added {len(df_file_class)} file/class relationships")
    
    if not df_ast.empty:
        all_dataframes.append(df_ast[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])
        print(f"🌳 Added {len(df_ast)} AST relationships")
    
    if not df_llm_lineage.empty:
        all_dataframes.append(df_llm_lineage[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])
        print(f"🤖 Added {len(df_llm_lineage)} LLM relationships")
    
    if not df_transformations.empty:
        all_dataframes.append(df_transformations[['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']])
        print(f"🔄 Added {len(df_transformations)} transformation relationships")
    
    if not all_dataframes:
        print("❌ No data to consolidate")
        return pd.DataFrame()
    
    # Consolidate all relationships
    df_consolidated = pd.concat(all_dataframes, ignore_index=True)
    
    # Enhanced deduplication with validation
    print(f"🔍 Deduplicating {len(df_consolidated)} total relationships...")
    
    # Remove exact duplicates
    df_consolidated = df_consolidated.drop_duplicates()
    
    # Remove invalid relationships (empty nodes)
    df_consolidated = df_consolidated[
        (df_consolidated['source_node'].notna()) & 
        (df_consolidated['destination_node'].notna()) &
        (df_consolidated['source_node'] != '') & 
        (df_consolidated['destination_node'] != '')
    ]
    
    # Remove self-references (node pointing to itself)
    df_consolidated = df_consolidated[
        df_consolidated['source_node'] != df_consolidated['destination_node']
    ]
    
    print(f"✅ Final consolidated dataset: {len(df_consolidated)} unique relationships")
    
    # Upload to Neo4j in batches
    batch_size = 100
    total_batches = (len(df_consolidated) + batch_size - 1) // batch_size
    
    print(f"📤 Uploading to Neo4j in {total_batches} batches...")
    
    for i in range(0, len(df_consolidated), batch_size):
        batch_df = df_consolidated.iloc[i:i+batch_size]
        batch_num = i // batch_size + 1
        
        try:
            # Create nodes and relationships
            for _, row in batch_df.iterrows():
                # Create source node
                graph.query(
                    f"MERGE (s:{row['source_type']} {{name: $source_name}})",
                    {'source_name': row['source_node']}
                )
                
                # Create destination node
                graph.query(
                    f"MERGE (d:{row['destination_type']} {{name: $dest_name}})",
                    {'dest_name': row['destination_node']}
                )
                
                # Create relationship
                graph.query(
                    f"MATCH (s:{row['source_type']} {{name: $source_name}}) "
                    f"MATCH (d:{row['destination_type']} {{name: $dest_name}}) "
                    f"MERGE (s)-[:{row['relationship']}]->(d)",
                    {
                        'source_name': row['source_node'],
                        'dest_name': row['destination_node']
                    }
                )
            
            print(f"✅ Batch {batch_num}/{total_batches} uploaded successfully")
            
        except Exception as e:
            print(f"❌ Error uploading batch {batch_num}: {e}")
            continue
    
    return df_consolidated

# Execute Stage 6: Consolidation and Neo4j Upload
print("🚀 Starting Stage 6: Consolidation and Neo4j Upload...")
df_final = consolidate_and_upload_to_neo4j()

# Store Stage 6 results
memory['stage_6_results'] = {
    'total_final_relationships': len(df_final),
    'unique_nodes': len(set(df_final['source_node'].tolist() + df_final['destination_node'].tolist())),
    'relationship_types': df_final['relationship'].nunique(),
    'node_types': len(set(df_final['source_type'].tolist() + df_final['destination_type'].tolist()))
}

save_memory(memory)

print(f"✅ Stage 6 Complete: {len(df_final)} relationships uploaded to Neo4j")
print(f"🎯 Unique Nodes: {memory['stage_6_results']['unique_nodes']}, Relationship Types: {memory['stage_6_results']['relationship_types']}")
print(f"📊 Node Types: {memory['stage_6_results']['node_types']}")