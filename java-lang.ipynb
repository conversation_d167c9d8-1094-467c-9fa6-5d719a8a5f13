# ===================== IMPORTS =====================
import os
import re
import javalang
from collections import defaultdict
import json
from neo4j import GraphDatabase
from langchain_openai import AzureChatOpenAI
from langchain.chains import GraphCypherQAChain
from langchain_community.graphs import Neo4jGraph

import os
import re
import javalang
import json
from collections import defaultdict

# ===================== HELPER FUNCTIONS =====================

def normalize_node(node_type, full_name, file_path, class_name=None, method_name=None):
    """Create standardized node metadata with proper names for Neo4j."""
    short_name = os.path.basename(full_name) if node_type in ("file", "folder") else full_name.split(".")[-1]
    if node_type == "file" and short_name.endswith(".java"):
        short_name = short_name[:-5]  # remove .java

    return {
        "id": f"{node_type}:{full_name}",
        "type": node_type,
        "name": short_name,
        "full_name": full_name,
        "file_path": file_path,
        "class_name": class_name,
        "method_name": method_name
    }

def add_node(nodes, node_type, full_name, file_path, class_name=None, method_name=None):
    node_id = f"{node_type}:{full_name}"
    if node_id not in nodes:
        nodes[node_id] = normalize_node(node_type, full_name, file_path, class_name, method_name)
    return node_id

def add_relation(relations, src, rel, dst):
    if src and dst:
        relations.add((src, rel, dst))

def extract_db_table_usage(code):
    tables = set()
    for m in re.finditer(r"\bFROM\s+(\w+)|\bINTO\s+(\w+)|\bUPDATE\s+(\w+)", code, flags=re.IGNORECASE):
        table = next(g for g in m.groups() if g)
        if table:
            tables.add(table)
    return tables

def extract_api_calls(code):
    apis = set()
    for m in re.finditer(r'@(?:GetMapping|PostMapping|PutMapping|DeleteMapping)\("([^"]+)"\)', code):
        apis.add(m.group(1))
    for m in re.finditer(r'["\'](https?://[^\s"\']+)["\']', code):
        apis.add(m.group(1))
    return apis

# ===================== MAIN EXTRACTION FUNCTION =====================

def extract_relations_v12(project_path):
    nodes = {}
    relations = set()
    collection_entities = {}
    class_uses_classes = defaultdict(set)

    # ========== PASS 0: Folder-to-Folder and Folder-to-File ==========
    java_files = []
    for root, dirs, files in os.walk(project_path):
        rel_root = os.path.relpath(root, project_path)
        folder_name = rel_root if rel_root != "." else os.path.basename(project_path)
        folder_node = add_node(nodes, "folder", folder_name, root)

        # folder → subfolder
        for d in dirs:
            subfolder_rel = os.path.relpath(os.path.join(root, d), project_path)
            subfolder_node = add_node(nodes, "folder", subfolder_rel, os.path.join(root, d))
            add_relation(relations, folder_node, "contains", subfolder_node)

        # folder → file
        for file in files:
            if file.endswith(".java"):
                file_rel = os.path.relpath(os.path.join(root, file), project_path)
                file_node = add_node(nodes, "file", file_rel, os.path.join(root, file))
                add_relation(relations, folder_node, "contains", file_node)
                java_files.append(os.path.join(root, file))

    # ========== PASS 1: Parse all Java files ==========
    parsed_files = {}
    for file_path in java_files:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            try:
                parsed_files[file_path] = (content, javalang.parse.parse(content))
            except javalang.parser.JavaSyntaxError:
                continue

    # ========== PASS 2: Detect MongoDB @Document collections ==========
    for file_path, (code, tree) in parsed_files.items():
        package_name = tree.package.name if tree.package else None
        for type_decl in tree.types:
            if isinstance(type_decl, javalang.tree.ClassDeclaration):
                full_class_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
                for annotation in type_decl.annotations:
                    if annotation.name == "Document":
                        for pair in annotation.element:
                            if pair.name == "collection":
                                collection = pair.value.value
                                collection_entities[full_class_name] = collection
                                add_node(nodes, "class", full_class_name, file_path)
                                add_node(nodes, "collection", collection, file_path)
                                add_relation(relations, f"class:{full_class_name}", "mapped_to_collection", f"collection:{collection}")

    # ========== PASS 3: Class, Method, Variable & Flow Extraction ==========
    for file_path, (code, tree) in parsed_files.items():
        rel_path = os.path.relpath(file_path, project_path)
        file_node = f"file:{rel_path}"

        # File-level DB tables and APIs
        for t in extract_db_table_usage(code):
            table_node = add_node(nodes, "table", t, file_path)
            add_relation(relations, file_node, "uses_table", table_node)

        for api in extract_api_calls(code):
            api_node = add_node(nodes, "api", api, file_path)
            add_relation(relations, file_node, "calls_api", api_node)

        # Imports for type resolution
        import_map = {imp.path.split('.')[-1]: imp.path for imp in tree.imports if imp.path and not imp.wildcard}
        package_name = tree.package.name if tree.package else None

        for type_decl in tree.types:
            if not isinstance(type_decl, (javalang.tree.ClassDeclaration, javalang.tree.InterfaceDeclaration)):
                continue

            decl_type = "class" if isinstance(type_decl, javalang.tree.ClassDeclaration) else "interface"
            full_decl_name = f"{package_name}.{type_decl.name}" if package_name else type_decl.name
            class_node = add_node(nodes, decl_type, full_decl_name, file_path)
            add_relation(relations, file_node, "declares", class_node)

            # Extends / Implements
            if type_decl.extends:
                base = type_decl.extends
                base_name = base.name if hasattr(base, "name") else str(base)
                parent_id = add_node(nodes, "class", import_map.get(base_name, base_name), file_path)
                add_relation(relations, class_node, "extends", parent_id)

            if getattr(type_decl, "implements", None):
                for impl in type_decl.implements:
                    parent_id = add_node(nodes, "interface", import_map.get(impl.name, impl.name), file_path)
                    add_relation(relations, class_node, "implements", parent_id)

            # Fields → variables
            for field in getattr(type_decl, "fields", []):
                for decl in field.declarators:
                    var_full = f"{full_decl_name}.{decl.name}"
                    var_node = add_node(nodes, "variable", var_full, file_path, class_name=full_decl_name)
                    add_relation(relations, class_node, "has_variable", var_node)

            # Methods → deep variable & flow
            for method in getattr(type_decl, "methods", []):
                method_full = f"{full_decl_name}.{method.name}"
                method_node = add_node(nodes, "method", method_full, file_path, class_name=full_decl_name)
                add_relation(relations, class_node, "has_method", method_node)

                declared_var_types = {}

                # Parameters
                for param in method.parameters:
                    param_full = f"{method_full}.{param.name}"
                    param_node = add_node(nodes, "variable", param_full, file_path, class_name=full_decl_name, method_name=method.name)
                    add_relation(relations, method_node, "has_parameter", param_node)
                    declared_var_types[param.name] = param.type.name if hasattr(param.type, "name") else None

                # Return variable
                if method.return_type:
                    return_full = f"{method_full}.return"
                    return_node = add_node(nodes, "variable", return_full, file_path, class_name=full_decl_name, method_name=method.name)
                    add_relation(relations, method_node, "returns", return_node)

                if not method.body:
                    continue

                for path, node in method:
                    # Local variable
                    if isinstance(node, javalang.tree.LocalVariableDeclaration):
                        for decl in node.declarators:
                            var_name = decl.name
                            var_full = f"{full_decl_name}.{method.name}.{var_name}"
                            var_node = add_node(nodes, "variable", var_full, file_path, class_name=full_decl_name, method_name=method.name)
                            add_relation(relations, method_node, "uses", var_node)
                            if hasattr(node.type, "name"):
                                declared_var_types[var_name] = node.type.name

                    # Method call
                    elif isinstance(node, javalang.tree.MethodInvocation):
                        called_method = f"method:{full_decl_name}.{node.member}"
                        if node.qualifier and node.qualifier in declared_var_types:
                            imp_class = import_map.get(declared_var_types[node.qualifier], declared_var_types[node.qualifier])
                            called_method = f"method:{imp_class}.{node.member}"
                        elif node.qualifier:
                            called_method = f"method:{node.qualifier}.{node.member}"

                        add_relation(relations, method_node, "calls", called_method)

                        for arg in node.arguments:
                            if isinstance(arg, javalang.tree.MemberReference):
                                var_node = f"variable:{full_decl_name}.{method.name}.{arg.member}"
                                add_relation(relations, called_method, "uses", var_node)

                    # Assignment → variable transformation
                    elif isinstance(node, javalang.tree.Assignment):
                        left = getattr(node, "expressionl", None) or getattr(node, "left", None)
                        right = node.value
                        if isinstance(left, javalang.tree.MemberReference):
                            left_var = f"variable:{full_decl_name}.{method.name}.{left.member}"
                            add_relation(relations, method_node, "declares", left_var)

                            # flows_to
                            if isinstance(right, javalang.tree.MemberReference):
                                right_var = f"variable:{full_decl_name}.{method.name}.{right.member}"
                                add_relation(relations, right_var, "flows_to", left_var)

                            elif isinstance(right, javalang.tree.MethodInvocation):
                                called_method = f"method:{full_decl_name}.{right.member}"
                                add_relation(relations, called_method, "assigns", left_var)
                                add_relation(relations, method_node, "calls", called_method)

    return {
        "nodes": list(nodes.values()),
        "relations": [list(r) for r in relations]
    }


# ===================== EXTRACT & SAVE =====================

project_path = "OneInsights"  # <-- your project path
json_path = "graph_v12.json"

graph_data = extract_relations_v12(project_path)
with open(json_path, "w") as f:
    json.dump(graph_data, f, indent=2)
print(f"Graph JSON saved: {json_path}, Nodes={len(graph_data['nodes'])}, Relations={len(graph_data['relations'])}")



from neo4j import GraphDatabase
import json

def load_graph_to_neo4j(json_path, uri, user, password, db="neo4j", wipe_existing=True):
    with open(json_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    nodes = data["nodes"]
    relations = data["relations"]
    driver = GraphDatabase.driver(uri, auth=(user, password))

    def clear_db(tx):
        tx.run("MATCH (n) DETACH DELETE n")

    def upload(tx):
        for node in nodes:
            label = node["type"].capitalize()
            tx.run(f"""
                MERGE (n:{label} {{id: $id}})
                SET n += $props
            """, id=node["id"], props=node)

        for src_id, rel_type, dst_id in relations:
            tx.run(f"""
                MATCH (a {{id: $src}})
                MATCH (b {{id: $dst}})
                MERGE (a)-[r:`{rel_type}`]->(b)
            """, src=src_id, dst=dst_id)

    with driver.session(database=db) as session:
        if wipe_existing:
            session.execute_write(clear_db)
        session.execute_write(upload)

    driver.close()
    print(f"✅ Graph uploaded to Neo4j: {len(nodes)} nodes, {len(relations)} relationships")


load_graph_to_neo4j("graph_v12.json", NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)


# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "final"




# ===================== CONNECT LANGCHAIN =====================
llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

graph = Neo4jGraph(
    url=NEO4J_URI,
    username=NEO4J_USER,
    password=NEO4J_PASSWORD,
    database=NEO4J_DB
)

chain = GraphCypherQAChain.from_llm(llm=llm, graph=graph, verbose=True, allow_dangerous_requests=True)

response = chain.invoke({"query": "how many classes and methods we have?"})
print(response)


from langchain.prompts import PromptTemplate

cypher_prompt = PromptTemplate(
    template="""You are an expert Neo4j Cypher generator.

Task:
- Translate the user question into a single valid Cypher query.
- Output ONLY the Cypher query, nothing else.
- Do not include explanations or natural language.
- Alias nodes with lowercase variables.
- Use DISTINCT on variables, not labels.
- Return results with meaningful aliases.

Question: {question}
Cypher:""",
    input_variables=["question"]
)


qa_chain = GraphCypherQAChain.from_llm(
    llm=llm,
    graph=graph,
    verbose=True,
    allow_dangerous_requests=True,
    cypher_prompt=cypher_prompt,
    return_intermediate_steps=True
)


from langchain.memory import ConversationBufferMemory
def ask_question(question):
    inputs = {
        "query": question,
        "chat_history": memory.load_memory_variables({})["chat_history"]
    }
    result = qa_chain.invoke(inputs)

    # ✅ Save only string output to memory
    memory.save_context(
        {"query": question},
        {"result": result["result"]}  
    )

    cypher = result["intermediate_steps"][0]["query"]
    print("Generated Cypher:\n", cypher)
    return result["result"]

print(ask_question("How many classes do we have?"))
print(ask_question("List first 5 class names"))
