source_node,source_type,destination_node,destination_type,relationship
OneInsights,Folder,ServicesBolt,Folder,CONTAINS
ServicesBolt,Folder,Application,File,CONTAINS
ServicesBolt,Folder,TriggerCollector,File,CONTAINS
ServicesBolt,Folder,Api,Folder,CONTAINS
Api,Folder,ALMConfigController,File,CONTAINS
Api,Folder,AlmController,File,CONTAINS
ServicesBolt,Folder,Request,Folder,CONTAINS
Request,Folder,ALMConfigReq,File,CONTAINS
Request,Folder,ALMToolReq,File,CONTAINS
ServicesBolt,Folder,Response,Folder,CONTAINS
Response,Folder,Authentication,File,CONTAINS
Response,Folder,DataResponse,File,CONTAINS
ServicesBolt,Folder,Service,Folder,CONTAINS
Service,Folder,ALMConfigService,File,CONTAINS
Service,Folder,ALMConfigServiceImplementation,File,CONTAINS
Service,Folder,AlmService,File,CONTAINS
Service,Folder,ALMServiceImplementation,File,CONTAINS
Service,Folder,ConfigurationSettingService,File,CONTAINS
Service,Folder,ConfigurationSettingServiceImplementation,File,CONTAINS
ServicesBolt,Folder,Util,Folder,CONTAINS
Util,Folder,DateUtil,File,CONTAINS
OneInsights,Folder,UnifiedBolt,Folder,CONTAINS
UnifiedBolt,Folder,Core,Folder,CONTAINS
Core,Folder,Application,File,CONTAINS
Core,Folder,ConstantVariable,File,CONTAINS
Core,Folder,ProjectCollector,File,CONTAINS
Core,Folder,Config,Folder,CONTAINS
Config,Folder,Configuration,File,CONTAINS
Config,Folder,DataConfig,File,CONTAINS
Config,Folder,MongoAggregate,File,CONTAINS
Core,Folder,Model,Folder,CONTAINS
Model,Folder,ALMConfiguration,File,CONTAINS
Model,Folder,BaseModel,File,CONTAINS
Model,Folder,ChangeHistoryModel,File,CONTAINS
Model,Folder,ComponentVelocityList,File,CONTAINS
Model,Folder,ConfigurationSetting,File,CONTAINS
Model,Folder,ConfigurationToolInfoMetric,File,CONTAINS
Model,Folder,CustomFields,File,CONTAINS
Model,Folder,IterationModel,File,CONTAINS
Model,Folder,IterationOutModel,File,CONTAINS
Model,Folder,MetricsModel,File,CONTAINS
Model,Folder,MonogOutMetrics,File,CONTAINS
Model,Folder,ScoreCardSprintData,File,CONTAINS
Model,Folder,TransitionModel,File,CONTAINS
Model,Folder,VelocityList,File,CONTAINS
Core,Folder,Repository,Folder,CONTAINS
Repository,Folder,ALMConfigRepo,File,CONTAINS
Repository,Folder,ChangeHisortyRepo,File,CONTAINS
Repository,Folder,ConfigurationSettingRep,File,CONTAINS
Repository,Folder,IterationRepo,File,CONTAINS
Repository,Folder,MetricRepo,File,CONTAINS
Repository,Folder,TransitionRepo,File,CONTAINS
UnifiedBolt,Folder,Jira,Folder,CONTAINS
Jira,Folder,ALMClientImplementation,File,CONTAINS
Jira,Folder,ChartCalculations,File,CONTAINS
Jira,Folder,CustomFieldNames,File,CONTAINS
Jira,Folder,DeleteJiraIssues,File,CONTAINS
Jira,Folder,EffortAndChangeItemInfo,File,CONTAINS
Jira,Folder,IssueHierarchy,File,CONTAINS
Jira,Folder,IterationInfo,File,CONTAINS
Jira,Folder,JIRAApplication,File,CONTAINS
Jira,Folder,JiraAuthentication,File,CONTAINS
Jira,Folder,JIRAClient,File,CONTAINS
Jira,Folder,JiraExceptions,File,CONTAINS
Jira,Folder,MetricsInfo,File,CONTAINS
Jira,Folder,RallyAuthentication,File,CONTAINS
Jira,Folder,ReleaseInfo,File,CONTAINS
Jira,Folder,SprintWiseCalculation,File,CONTAINS
Jira,Folder,TransitionInfo,File,CONTAINS
Jira,Folder,TransitionMetrices,File,CONTAINS
UnifiedBolt,Folder,Util,Folder,CONTAINS
Util,Folder,BacklogCalculation,File,CONTAINS
Util,Folder,BuildCalculations,File,CONTAINS
Util,Folder,CommonFunctions,File,CONTAINS
Util,Folder,Constant,File,CONTAINS
Util,Folder,CryptoUtils,File,CONTAINS
Util,Folder,DefectCalculations,File,CONTAINS
Util,Folder,EncryptionDecryptionAES,File,CONTAINS
Util,Folder,EncryptorAesGcmPassword,File,CONTAINS
Util,Folder,RestClient,File,CONTAINS
Util,Folder,SprintProgress,File,CONTAINS
Util,Folder,SprintProgressCalculations,File,CONTAINS
Util,Folder,StoryProgressModel,File,CONTAINS
Util,Folder,StoryProgressSprintwise,File,CONTAINS
Util,Folder,TaskRiskSprint,File,CONTAINS
Util,Folder,TeamQualityUtils,File,CONTAINS
Util,Folder,VelocityCalculations,File,CONTAINS
Application,File,Application,Class,DECLARES
TriggerCollector,File,TriggerCollector,Class,DECLARES
ALMConfigController,File,ALMConfigController,Class,DECLARES
AlmController,File,AlmController,Class,DECLARES
ALMConfigReq,File,ALMConfigReq,Class,DECLARES
ALMToolReq,File,ALMToolReq,Class,DECLARES
DataResponse,File,DataResponse,Class,DECLARES
ALMConfigServiceImplementation,File,ALMConfigServiceImplementation,Class,DECLARES
ALMServiceImplementation,File,ALMServiceImplementation,Class,DECLARES
ALMServiceImplementation,File,TransitionComparator,Class,DECLARES
ALMServiceImplementation,File,SprintComparatort,Class,DECLARES
ConfigurationSettingServiceImplementation,File,ConfigurationSettingServiceImplementation,Class,DECLARES
DateUtil,File,DateUtil,Class,DECLARES
ConstantVariable,File,ConstantVariable,Class,DECLARES
ProjectCollector,File,ProjectCollector,Class,DECLARES
Configuration,File,Configuration,Class,DECLARES
DataConfig,File,DataConfig,Class,DECLARES
MongoAggregate,File,MongoAggregate,Class,DECLARES
ALMConfiguration,File,ALMConfiguration,Class,DECLARES
BaseModel,File,BaseModel,Class,DECLARES
ChangeHistoryModel,File,ChangeHistoryModel,Class,DECLARES
ComponentVelocityList,File,ComponentVelocityList,Class,DECLARES
ConfigurationSetting,File,ConfigurationSetting,Class,DECLARES
ConfigurationToolInfoMetric,File,ConfigurationToolInfoMetric,Class,DECLARES
CustomFields,File,CustomFields,Class,DECLARES
IterationModel,File,IterationModel,Class,DECLARES
IterationOutModel,File,IterationOutModel,Class,DECLARES
MetricsModel,File,MetricsModel,Class,DECLARES
MonogOutMetrics,File,MonogOutMetrics,Class,DECLARES
ScoreCardSprintData,File,ScoreCardSprintData,Class,DECLARES
TransitionModel,File,TransitionModel,Class,DECLARES
VelocityList,File,VelocityList,Class,DECLARES
ALMClientImplementation,File,ALMClientImplementation,Class,DECLARES
ChartCalculations,File,ChartCalculations,Class,DECLARES
ChartCalculations,File,TransitionComparator,Class,DECLARES
ChartCalculations,File,SprintComparatort,Class,DECLARES
CustomFieldNames,File,CustomFieldNames,Class,DECLARES
DeleteJiraIssues,File,DeleteJiraIssues,Class,DECLARES
EffortAndChangeItemInfo,File,EffortAndChangeItemInfo,Class,DECLARES
IssueHierarchy,File,IssueHierarchy,Class,DECLARES
IterationInfo,File,IterationInfo,Class,DECLARES
JIRAApplication,File,JIRAApplication,Class,DECLARES
JiraAuthentication,File,JiraAuthentication,Class,DECLARES
JiraExceptions,File,JiraExceptions,Class,DECLARES
MetricsInfo,File,MetricsInfo,Class,DECLARES
RallyAuthentication,File,RallyAuthentication,Class,DECLARES
ReleaseInfo,File,ReleaseInfo,Class,DECLARES
SprintWiseCalculation,File,SprintWiseCalculation,Class,DECLARES
TransitionInfo,File,TransitionInfo,Class,DECLARES
TransitionMetrices,File,TransitionMetrices,Class,DECLARES
BacklogCalculation,File,BacklogCalculation,Class,DECLARES
BuildCalculations,File,BuildCalculations,Class,DECLARES
CommonFunctions,File,CommonFunctions,Class,DECLARES
Constant,File,Constant,Class,DECLARES
CryptoUtils,File,CryptoUtils,Class,DECLARES
DefectCalculations,File,DefectCalculations,Class,DECLARES
DefectCalculations,File,ValueComparator,Class,DECLARES
DefectCalculations,File,SprintComparatort,Class,DECLARES
EncryptionDecryptionAES,File,EncryptionDecryptionAES,Class,DECLARES
EncryptorAesGcmPassword,File,EncryptorAesGcmPassword,Class,DECLARES
RestClient,File,RestClient,Class,DECLARES
SprintProgress,File,SprintProgress,Class,DECLARES
SprintProgress,File,LogDateComparator,Class,DECLARES
SprintProgress,File,SprintComparator,Class,DECLARES
SprintProgressCalculations,File,SprintProgressCalculations,Class,DECLARES
StoryProgressModel,File,StoryProgressModel,Class,DECLARES
StoryProgressSprintwise,File,StoryProgressSprintwise,Class,DECLARES
TaskRiskSprint,File,TaskRiskSprint,Class,DECLARES
TeamQualityUtils,File,TeamQualityUtils,Class,DECLARES
VelocityCalculations,File,VelocityCalculations,Class,DECLARES
Application,Class,configure,Method,DECLARES
Application,Class,passwordEncoder,Method,DECLARES
Application,Class,main,Method,DECLARES
TriggerCollector,Class,Logger,Variable,HAS_FIELD
TriggerCollector,Class,Ctx,Variable,HAS_FIELD
TriggerCollector,Class,getDataFromTools,Method,DECLARES
getDataFromTools,Method,Ctx,Variable,USES
getDataFromTools,Method,Portfolio,Variable,USES
getDataFromTools,Method,Scheduler,Variable,USES
getDataFromTools,Method,Jobkey,Variable,USES
getDataFromTools,Method,Job,Variable,USES
getDataFromTools,Method,Joba,Variable,USES
getDataFromTools,Method,Trigger,Variable,USES
ALMConfigController,Class,Log,Variable,HAS_FIELD
ALMConfigController,Class,Almconfigservice,Variable,HAS_FIELD
ALMConfigController,Class,saveALMConfig,Method,DECLARES
ALMConfigController,Class,retrieveList,Method,DECLARES
ALMConfigController,Class,retrieveALMConfig,Method,DECLARES
ALMConfigController,Class,/Almconfig,Endpoint,DECLARES
ALMConfigController,Class,/Almconfigdetails,Endpoint,DECLARES
ALMConfigController,Class,/Almconfigdetailsconfig,Endpoint,DECLARES
AlmController,Class,Service,Variable,HAS_FIELD
AlmController,Class,storyAgeing,Method,DECLARES
AlmController,Class,groomingTable,Method,DECLARES
AlmController,Class,delDuplicate,Method,DECLARES
AlmController,Class,getSprintProgressHome,Method,DECLARES
AlmController,Class,defectInsightData,Method,DECLARES
AlmController,Class,defectTrendAndClassification,Method,DECLARES
AlmController,Class,defectClassification,Method,DECLARES
AlmController,Class,getIssueBrakeUp,Method,DECLARES
AlmController,Class,getStoryProgress,Method,DECLARES
AlmController,Class,getDefectsSummaryHome,Method,DECLARES
AlmController,Class,getTaskRiskStoryPoint,Method,DECLARES
AlmController,Class,burndownCalculation,Method,DECLARES
AlmController,Class,getProductionSlippage,Method,DECLARES
AlmController,Class,getDefectDensity,Method,DECLARES
AlmController,Class,getDefectBacklog,Method,DECLARES
AlmController,Class,getDefectPareto,Method,DECLARES
AlmController,Class,getActiveSprints,Method,DECLARES
AlmController,Class,delAllIsues,Method,DECLARES
AlmController,Class,getMetricsDatas,Method,DECLARES
AlmController,Class,getAllTransitions,Method,DECLARES
AlmController,Class,getProjectMetrics,Method,DECLARES
AlmController,Class,getChangesItems,Method,DECLARES
AlmController,Class,getTransitionsData,Method,DECLARES
AlmController,Class,getIterationData,Method,DECLARES
AlmController,Class,getEffortData,Method,DECLARES
AlmController,Class,getProjectDetials,Method,DECLARES
AlmController,Class,getCurrentProjectDetials,Method,DECLARES
AlmController,Class,getCurrentIter,Method,DECLARES
AlmController,Class,getIterations,Method,DECLARES
AlmController,Class,getDefectCount,Method,DECLARES
AlmController,Class,getRelease,Method,DECLARES
AlmController,Class,getUnReleaseData,Method,DECLARES
getUnReleaseData,Method,Response,Variable,USES
AlmController,Class,getDefects,Method,DECLARES
getDefects,Method,Response,Variable,USES
AlmController,Class,getSlaData,Method,DECLARES
AlmController,Class,getAssigneeIssues,Method,DECLARES
AlmController,Class,getDateIterations,Method,DECLARES
AlmController,Class,getProdDefects,Method,DECLARES
getProdDefects,Method,Response,Variable,USES
AlmController,Class,getAlmType,Method,DECLARES
getAlmType,Method,Almtype,Variable,USES
getAlmType,Method,Config,Variable,USES
getAlmType,Method,Metric,Variable,USES
getAlmType,Method,Configuration1,Variable,USES
getAlmType,Method,Metric1,Variable,USES
AlmController,Class,getVelocityChart,Method,DECLARES
AlmController,Class,getIssueHierarchy,Method,DECLARES
getIssueHierarchy,Method,Response,Variable,USES
AlmController,Class,getComponentWiseIssueHierarchy,Method,DECLARES
getComponentWiseIssueHierarchy,Method,Response,Variable,USES
AlmController,Class,getComponentWiseVelocityChart,Method,DECLARES
getComponentWiseVelocityChart,Method,Resp,Variable,USES
AlmController,Class,getComponontWiseSprintWiseStories,Method,DECLARES
getComponontWiseSprintWiseStories,Method,Response,Variable,USES
AlmController,Class,getComponents,Method,DECLARES
getComponents,Method,Response,Variable,USES
AlmController,Class,updateComponent,Method,DECLARES
AlmController,Class,saveEngScore,Method,DECLARES
AlmController,Class,getFeatureMetrics,Method,DECLARES
ALMConfigReq,Class,Storyname,Variable,HAS_FIELD
ALMConfigReq,Class,Priorityname,Variable,HAS_FIELD
ALMConfigReq,Class,Projectname,Variable,HAS_FIELD
ALMConfigReq,Class,Defectname,Variable,HAS_FIELD
ALMConfigReq,Class,Releasename,Variable,HAS_FIELD
ALMConfigReq,Class,Taskname,Variable,HAS_FIELD
ALMConfigReq,Class,Closestate,Variable,HAS_FIELD
ALMConfigReq,Class,Newstate,Variable,HAS_FIELD
ALMConfigReq,Class,Progressstate,Variable,HAS_FIELD
ALMConfigReq,Class,Criticalpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Highpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Medpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Lowpriority,Variable,HAS_FIELD
ALMConfigReq,Class,Tracksset,Variable,HAS_FIELD
ALMConfigReq,Class,Rejectionphase,Variable,HAS_FIELD
ALMConfigReq,Class,Reopenphase,Variable,HAS_FIELD
ALMConfigReq,Class,Testingphase,Variable,HAS_FIELD
ALMConfigReq,Class,Productionphase,Variable,HAS_FIELD
ALMConfigReq,Class,Personhours,Variable,HAS_FIELD
ALMConfigReq,Class,Timezone,Variable,HAS_FIELD
ALMConfigReq,Class,Velocityfields,Variable,HAS_FIELD
ALMConfigReq,Class,Environment,Variable,HAS_FIELD
ALMConfigReq,Class,Safeenabled,Variable,HAS_FIELD
ALMConfigReq,Class,Ccrlabel,Variable,HAS_FIELD
ALMConfigReq,Class,Cycletimestates,Variable,HAS_FIELD
ALMConfigReq,Class,Throughputstates,Variable,HAS_FIELD
ALMConfigReq,Class,Firstsprint,Variable,HAS_FIELD
ALMConfigReq,Class,Trendtype,Variable,HAS_FIELD
ALMConfigReq,Class,getCcrLabel,Method,DECLARES
ALMConfigReq,Class,setCcrLabel,Method,DECLARES
ALMConfigReq,Class,getCycleTimeStates,Method,DECLARES
ALMConfigReq,Class,setCycleTimeStates,Method,DECLARES
ALMConfigReq,Class,getThroughputStates,Method,DECLARES
ALMConfigReq,Class,setThroughputStates,Method,DECLARES
ALMConfigReq,Class,getRejectionPhase,Method,DECLARES
ALMConfigReq,Class,setRejectionPhase,Method,DECLARES
ALMConfigReq,Class,getReopenPhase,Method,DECLARES
ALMConfigReq,Class,setReopenPhase,Method,DECLARES
ALMConfigReq,Class,getTestingPhase,Method,DECLARES
ALMConfigReq,Class,setTestingPhase,Method,DECLARES
ALMConfigReq,Class,getProductionPhase,Method,DECLARES
ALMConfigReq,Class,setProductionPhase,Method,DECLARES
ALMConfigReq,Class,getStoryName,Method,DECLARES
ALMConfigReq,Class,getPriorityName,Method,DECLARES
ALMConfigReq,Class,setPriorityName,Method,DECLARES
ALMConfigReq,Class,getTaskName,Method,DECLARES
ALMConfigReq,Class,setTaskName,Method,DECLARES
ALMConfigReq,Class,getReleaseName,Method,DECLARES
ALMConfigReq,Class,setReleaseName,Method,DECLARES
ALMConfigReq,Class,getCloseState,Method,DECLARES
ALMConfigReq,Class,setCloseState,Method,DECLARES
ALMConfigReq,Class,getCriticalPriority,Method,DECLARES
ALMConfigReq,Class,setCriticalPriority,Method,DECLARES
ALMConfigReq,Class,getHighPriority,Method,DECLARES
ALMConfigReq,Class,setHighPriority,Method,DECLARES
ALMConfigReq,Class,getMedPriority,Method,DECLARES
ALMConfigReq,Class,setMedPriority,Method,DECLARES
ALMConfigReq,Class,getLowPriority,Method,DECLARES
ALMConfigReq,Class,setLowPriority,Method,DECLARES
ALMConfigReq,Class,setStoryName,Method,DECLARES
ALMConfigReq,Class,getProjectName,Method,DECLARES
ALMConfigReq,Class,setProjectName,Method,DECLARES
ALMConfigReq,Class,getDefectName,Method,DECLARES
ALMConfigReq,Class,setDefectName,Method,DECLARES
ALMConfigReq,Class,getTrendType,Method,DECLARES
ALMConfigReq,Class,setTrendType,Method,DECLARES
ALMConfigReq,Class,getTracksSet,Method,DECLARES
ALMConfigReq,Class,setTracksSet,Method,DECLARES
ALMConfigReq,Class,getNewState,Method,DECLARES
ALMConfigReq,Class,setNewState,Method,DECLARES
ALMConfigReq,Class,getProgressState,Method,DECLARES
ALMConfigReq,Class,setProgressState,Method,DECLARES
ALMConfigReq,Class,toDetailsAddSetting,Method,DECLARES
ALMConfigReq,Class,getPersonHours,Method,DECLARES
ALMConfigReq,Class,setPersonHours,Method,DECLARES
ALMConfigReq,Class,getTimeZone,Method,DECLARES
ALMConfigReq,Class,setTimeZone,Method,DECLARES
ALMConfigReq,Class,getVelocityFields,Method,DECLARES
ALMConfigReq,Class,setVelocityFields,Method,DECLARES
ALMConfigReq,Class,getEnvironment,Method,DECLARES
ALMConfigReq,Class,setEnvironment,Method,DECLARES
ALMConfigReq,Class,isSafeEnabled,Method,DECLARES
ALMConfigReq,Class,setSafeEnabled,Method,DECLARES
ALMConfigReq,Class,getFirstSprint,Method,DECLARES
ALMConfigReq,Class,setFirstSprint,Method,DECLARES
ALMToolReq,Class,Almtype,Variable,HAS_FIELD
ALMToolReq,Class,getAlmType,Method,DECLARES
ALMToolReq,Class,setAlmType,Method,DECLARES
setAlmType,Method,Almtype,Variable,USES
Authentication,Class,Authenticationofservice,Method,DECLARES
Authentication,Class,Authntication,Variable,HAS_FIELD
Auth,Class,Response,Variable,HAS_FIELD
Auth,Class,Authenticationstatus,Variable,HAS_FIELD
Auth,Class,Authenticate,Method,DECLARES
Authenticate,Method,Feature,Variable,USES
Authenticate,Method,Client,Variable,USES
Authenticate,Method,Webtarget,Variable,USES
Authenticate,Method,Invocationbuilder,Variable,USES
DataResponse,Class,Lastupdated,Variable,HAS_FIELD
DataResponse,Class,getResult,Method,DECLARES
DataResponse,Class,getLastUpdated,Method,DECLARES
Almconfigservice,Interface,saveALMConfig,Method,DECLARES
Almconfigservice,Interface,retrieveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ALMConfigServiceImplementation,Class,Log,Variable,HAS_FIELD
ALMConfigServiceImplementation,Class,saveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,retrieveALMConfig,Method,DECLARES
retrieveALMConfig,Method,Lastupdate,Variable,USES
Almservice,Interface,getMetricDetails,Method,DECLARES
Almservice,Interface,getAllMetrics,Method,DECLARES
Almservice,Interface,getChangesItems,Method,DECLARES
Almservice,Interface,getTransitionsData,Method,DECLARES
Almservice,Interface,getIterationData,Method,DECLARES
Almservice,Interface,getEffortData,Method,DECLARES
Almservice,Interface,getProjectDetails,Method,DECLARES
Almservice,Interface,getDefectCounts,Method,DECLARES
Almservice,Interface,getCrtItr,Method,DECLARES
Almservice,Interface,getRelease,Method,DECLARES
Almservice,Interface,getUnReleaseData,Method,DECLARES
Almservice,Interface,getDefects,Method,DECLARES
Almservice,Interface,getSlaData,Method,DECLARES
Almservice,Interface,getAssigneeIssues,Method,DECLARES
Almservice,Interface,getDateIterations,Method,DECLARES
Almservice,Interface,getProdDefects,Method,DECLARES
Almservice,Interface,delDuplicate,Method,DECLARES
Almservice,Interface,getCurrentProjectDetails,Method,DECLARES
Almservice,Interface,delAllIssues,Method,DECLARES
Almservice,Interface,getAllTransitions,Method,DECLARES
Almservice,Interface,getComponentVelocity,Method,DECLARES
Almservice,Interface,getComponentsSprint,Method,DECLARES
Almservice,Interface,getIssueHierarchy,Method,DECLARES
Almservice,Interface,getComponentWiseIssueHierarchy,Method,DECLARES
Almservice,Interface,getComponents,Method,DECLARES
Almservice,Interface,updateComponentsOfTaskandSubtask,Method,DECLARES
Almservice,Interface,getFeatureMetrics,Method,DECLARES
Almservice,Interface,getSprintProgressHome,Method,DECLARES
Almservice,Interface,getDefectsSummaryHome,Method,DECLARES
Almservice,Interface,getTaskRisk,Method,DECLARES
Almservice,Interface,getActiveSprints,Method,DECLARES
Almservice,Interface,getIssueBrakeUp,Method,DECLARES
Almservice,Interface,getStoryProgress,Method,DECLARES
Almservice,Interface,burndownCalculation,Method,DECLARES
Almservice,Interface,getDefectInsightData,Method,DECLARES
Almservice,Interface,defectParetoCalculation,Method,DECLARES
Almservice,Interface,getProductionSlippage,Method,DECLARES
Almservice,Interface,getDefectDensity,Method,DECLARES
Almservice,Interface,getDefectBacklog,Method,DECLARES
Almservice,Interface,getDefectTrendAndClassification,Method,DECLARES
Almservice,Interface,getStoryAgeingData,Method,DECLARES
Almservice,Interface,getGroomingTable,Method,DECLARES
Almservice,Interface,getAllIterations,Method,DECLARES
Almservice,Interface,getDefectClassification,Method,DECLARES
Almservice,Interface,saveEngScore,Method,DECLARES
Almservice,Interface,getComponentVelocityChart,Method,DECLARES
Almservice,Interface,getComponentsSprintStories,Method,DECLARES
Almservice,Interface,getIssueHierarchyChart,Method,DECLARES
Almservice,Interface,getComponentWiseIssueHierarchyChart,Method,DECLARES
Almservice,Interface,getComponentsChart,Method,DECLARES
ALMServiceImplementation,Class,Transitionrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Efforthistoryrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Changehisortyrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Authorrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Projectrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Releaserepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Iterationrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Metricrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,Agg,Variable,HAS_FIELD
ALMServiceImplementation,Class,Mongotemplate,Variable,HAS_FIELD
ALMServiceImplementation,Class,Workingbacklog,Variable,HAS_FIELD
ALMServiceImplementation,Class,Workingsprints,Variable,HAS_FIELD
ALMServiceImplementation,Class,Velocityfields,Variable,HAS_FIELD
ALMServiceImplementation,Class,Closestates,Variable,HAS_FIELD
ALMServiceImplementation,Class,Tasknames,Variable,HAS_FIELD
ALMServiceImplementation,Class,Almconfig,Variable,HAS_FIELD
ALMServiceImplementation,Class,Vlist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Issuelist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Widarr,Variable,HAS_FIELD
ALMServiceImplementation,Class,Tempsprefined,Variable,HAS_FIELD
ALMServiceImplementation,Class,Tempspremoved,Variable,HAS_FIELD
ALMServiceImplementation,Class,Finalstoriescommited,Variable,HAS_FIELD
ALMServiceImplementation,Class,Storiescompleted,Variable,HAS_FIELD
ALMServiceImplementation,Class,Defetcscompleted,Variable,HAS_FIELD
ALMServiceImplementation,Class,Refinedissulist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Removedissulist,Variable,HAS_FIELD
ALMServiceImplementation,Class,Logger,Variable,HAS_FIELD
ALMServiceImplementation,Class,Chartservice,Variable,HAS_FIELD
ALMServiceImplementation,Class,getMetricDetails,Method,DECLARES
ALMServiceImplementation,Class,getAllMetrics,Method,DECLARES
ALMServiceImplementation,Class,getChangesItems,Method,DECLARES
ALMServiceImplementation,Class,getTransitionsData,Method,DECLARES
ALMServiceImplementation,Class,getIterationData,Method,DECLARES
ALMServiceImplementation,Class,getEffortData,Method,DECLARES
ALMServiceImplementation,Class,getProjectDetails,Method,DECLARES
ALMServiceImplementation,Class,getFeatureMetrics,Method,DECLARES
ALMServiceImplementation,Class,populateAuthor,Method,DECLARES
ALMServiceImplementation,Class,getCrtItr,Method,DECLARES
ALMServiceImplementation,Class,getDefectCounts,Method,DECLARES
ALMServiceImplementation,Class,getRelease,Method,DECLARES
ALMServiceImplementation,Class,getUnReleaseData,Method,DECLARES
ALMServiceImplementation,Class,getDefects,Method,DECLARES
ALMServiceImplementation,Class,getProdDefects,Method,DECLARES
ALMServiceImplementation,Class,getDateIterations,Method,DECLARES
ALMServiceImplementation,Class,getSlaData,Method,DECLARES
ALMServiceImplementation,Class,getAssigneeIssues,Method,DECLARES
ALMServiceImplementation,Class,delDuplicate,Method,DECLARES
ALMServiceImplementation,Class,getCurrentProjectDetails,Method,DECLARES
ALMServiceImplementation,Class,delAllIssues,Method,DECLARES
ALMServiceImplementation,Class,getAllTransitions,Method,DECLARES
ALMServiceImplementation,Class,getComponentVelocity,Method,DECLARES
ALMServiceImplementation,Class,getComponentsSprint,Method,DECLARES
ALMServiceImplementation,Class,getIssueHierarchy,Method,DECLARES
ALMServiceImplementation,Class,getComponentList,Method,DECLARES
ALMServiceImplementation,Class,getComponentWiseIssueHierarchy,Method,DECLARES
ALMServiceImplementation,Class,getComponents,Method,DECLARES
ALMServiceImplementation,Class,getSprintWiseStories,Method,DECLARES
ALMServiceImplementation,Class,filterTrans,Method,DECLARES
ALMServiceImplementation,Class,getVelocityChart,Method,DECLARES
ALMServiceImplementation,Class,callSP,Method,DECLARES
ALMServiceImplementation,Class,calcClosedSP,Method,DECLARES
ALMServiceImplementation,Class,storyLoop,Method,DECLARES
ALMServiceImplementation,Class,storyLoopRefined,Method,DECLARES
TransitionComparator,Class,compare,Method,DECLARES
getComponentsSprint,Method,Iterationrepo,Variable,USES
getComponentsSprint,Method,Metricrepo,Variable,USES
getComponentsSprint,Method,Transitionrepo,Variable,USES
getComponentsSprint,Method,Authorrepo,Variable,USES
getProjectDetails,Method,Almty,Variable,USES
populateAuthor,Method,Datamodel,Variable,USES
SprintComparatort,Class,compare,Method,DECLARES
ALMServiceImplementation,Class,updateComponentsOfTaskandSubtask,Method,DECLARES
ALMServiceImplementation,Class,getSprintProgressHome,Method,DECLARES
ALMServiceImplementation,Class,getDefectsSummaryHome,Method,DECLARES
ALMServiceImplementation,Class,getTaskRisk,Method,DECLARES
ALMServiceImplementation,Class,getActiveSprints,Method,DECLARES
ALMServiceImplementation,Class,getIssueBrakeUp,Method,DECLARES
ALMServiceImplementation,Class,getStoryProgress,Method,DECLARES
ALMServiceImplementation,Class,burndownCalculation,Method,DECLARES
ALMServiceImplementation,Class,getDefectInsightData,Method,DECLARES
ALMServiceImplementation,Class,defectParetoCalculation,Method,DECLARES
ALMServiceImplementation,Class,getProductionSlippage,Method,DECLARES
ALMServiceImplementation,Class,getDefectDensity,Method,DECLARES
ALMServiceImplementation,Class,getDefectBacklog,Method,DECLARES
ALMServiceImplementation,Class,getDefectTrendAndClassification,Method,DECLARES
ALMServiceImplementation,Class,getDefectClassification,Method,DECLARES
ALMServiceImplementation,Class,getStoryAgeingData,Method,DECLARES
ALMServiceImplementation,Class,getGroomingTable,Method,DECLARES
ALMServiceImplementation,Class,getAllIterations,Method,DECLARES
ALMServiceImplementation,Class,getAlmType,Method,DECLARES
ALMServiceImplementation,Class,saveEngScore,Method,DECLARES
ALMServiceImplementation,Class,getComponentVelocityChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentsSprintStories,Method,DECLARES
ALMServiceImplementation,Class,getIssueHierarchyChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentWiseIssueHierarchyChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentsChart,Method,DECLARES
Configurationsettingservice,Interface,getConfig,Method,DECLARES
Configurationsettingservice,Interface,addConfig,Method,DECLARES
Configurationsettingservice,Interface,deleteConfig,Method,DECLARES
Configurationsettingservice,Interface,deleteAllCollections,Method,DECLARES
Configurationsettingservice,Interface,deleteProject,Method,DECLARES
Configurationsettingservice,Interface,getConfigProject,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,getConfig,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,addConfig,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,deleteConfig,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,deleteAllCollections,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,deleteProject,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,getConfigProject,Method,DECLARES
getConfig,Method,Lastupdated,Variable,USES
addConfig,Method,Date,Variable,USES
addConfig,Method,Timestamp,Variable,USES
deleteAllCollections,Method,Failurepattern,Variable,USES
deleteAllCollections,Method,Failurepatternobj,Variable,USES
getConfigProject,Method,Config,Variable,USES
ConfigurationSettingServiceImplementation,Class,Configurationsettingrepository,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Almservice,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Buildrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Buildfailurepatternrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Codecoveragerepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Codequalityrep,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Healthrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Scmrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Chartconfigrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Goalsettingrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Portfoliorepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Projecthealthrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Userassociation,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,Log,Variable,HAS_FIELD
DateUtil,Class,getDateInFormat,Method,DECLARES
getDateInFormat,Method,Pattern,Variable,USES
getDateInFormat,Method,Format,Variable,USES
getDateInFormat,Method,Simpledateformat,Variable,USES
DateUtil,Class,getLastWeekWorkingDateRange,Method,DECLARES
getLastWeekWorkingDateRange,Method,Date,Variable,USES
getLastWeekWorkingDateRange,Method,Start,Variable,USES
getLastWeekWorkingDateRange,Method,End,Variable,USES
ConstantVariable,Class,Name,Variable,HAS_FIELD
ConstantVariable,Class,Date,Variable,HAS_FIELD
ConstantVariable,Class,Version,Variable,HAS_FIELD
ConstantVariable,Class,Msr,Variable,HAS_FIELD
ConstantVariable,Class,Formatted_Value,Variable,HAS_FIELD
ConstantVariable,Class,Key,Variable,HAS_FIELD
ConstantVariable,Class,Alert_Text,Variable,HAS_FIELD
ConstantVariable,Class,Date_Format,Variable,HAS_FIELD
ConstantVariable,Class,Jobs_Url_For_Db,Variable,HAS_FIELD
ConstantVariable,Class,Smtp_Toaddress,Variable,HAS_FIELD
ConstantVariable,Class,Mail_Smtp_Auth,Variable,HAS_FIELD
ConstantVariable,Class,Mail_Smtp_Enable,Variable,HAS_FIELD
ConstantVariable,Class,Mail_Smtp_Host,Variable,HAS_FIELD
ConstantVariable,Class,Mail_Smtp_Port,Variable,HAS_FIELD
ConstantVariable,Class,Mail_Smtp_Ssl_Trust,Variable,HAS_FIELD
ConstantVariable,Class,Mail_Smtp_Ssl_Protocols,Variable,HAS_FIELD
ConstantVariable,Class,True_String,Variable,HAS_FIELD
ConstantVariable,Class,Brillio_Host,Variable,HAS_FIELD
ConstantVariable,Class,Brillio_Port,Variable,HAS_FIELD
ConstantVariable,Class,Bolt_User,Variable,HAS_FIELD
ConstantVariable,Class,Bolt_Pass,Variable,HAS_FIELD
ConstantVariable,Class,Sonar_Metrics,Variable,HAS_FIELD
ConstantVariable,Class,Sonar_Metrics_New1,Variable,HAS_FIELD
ConstantVariable,Class,Sonar_Metrics_New2,Variable,HAS_FIELD
ConstantVariable,Class,Sonar_Metrics_New3,Variable,HAS_FIELD
ConstantVariable,Class,Sonarcollectorstartdate,Variable,HAS_FIELD
ConstantVariable,Class,Build_Url_Pattern,Variable,HAS_FIELD
ConstantVariable,Class,Alm_Backlog,Variable,HAS_FIELD
ConstantVariable,Class,Alm_Created,Variable,HAS_FIELD
ConstantVariable,Class,Falseconst,Variable,HAS_FIELD
ConstantVariable,Class,Alm_Resolutiondate,Variable,HAS_FIELD
ConstantVariable,Class,Alm_Timeoriginalestimate,Variable,HAS_FIELD
ConstantVariable,Class,Keyword_Assignee,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Status,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Issuetype,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Value,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Timespent,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Timeestimate,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Story,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Priority,Variable,HAS_FIELD
ConstantVariable,Class,Kywrd_Uname,Variable,HAS_FIELD
ConstantVariable,Class,Jira_Sprint_Field,Variable,HAS_FIELD
ConstantVariable,Class,Jira_Sprint_Field_Brillio,Variable,HAS_FIELD
ConstantVariable,Class,Jira_Statuses,Variable,HAS_FIELD
ConstantVariable,Class,Updated,Variable,HAS_FIELD
ConstantVariable,Class,Timetracking,Variable,HAS_FIELD
ConstantVariable,Class,Collector_Status_Success,Variable,HAS_FIELD
ConstantVariable,Class,Collector_Status_Failure,Variable,HAS_FIELD
ConstantVariable,Class,Lastrunmap,Variable,HAS_FIELD
ConstantVariable,Class,Secret_Key,Variable,HAS_FIELD
ConstantVariable,Class,Salt,Variable,HAS_FIELD
ConstantVariable,Class,Palmtypeconst,Variable,HAS_FIELD
ConstantVariable,Class,Lowercaseactiveconst,Variable,HAS_FIELD
ConstantVariable,Class,Uppercaseactiveconst,Variable,HAS_FIELD
ConstantVariable,Class,Lowercasemetricsconst,Variable,HAS_FIELD
ConstantVariable,Class,Mixedcasemetricsconst,Variable,HAS_FIELD
ConstantVariable,Class,Withdrawnconst,Variable,HAS_FIELD
ConstantVariable,Class,Pnameconst,Variable,HAS_FIELD
ConstantVariable,Class,Activestates,Variable,HAS_FIELD
ConstantVariable,Class,Closedstates,Variable,HAS_FIELD
ConstantVariable,Class,Projectnamearr,Variable,HAS_FIELD
ConstantVariable,Class,Getapisadmin,Variable,HAS_FIELD
ConstantVariable,Class,Getapisuser,Variable,HAS_FIELD
ConstantVariable,Class,Postupddatedeleteapis,Variable,HAS_FIELD
ConstantVariable,Class,Postapiwithqueryparamuser,Variable,HAS_FIELD
ConstantVariable,Class,Postapiwithqueryparamuserlist,Variable,HAS_FIELD
ConstantVariable,Class,Postdeleteapiuser,Variable,HAS_FIELD
ConstantVariable,Class,Apibyusernamearr,Variable,HAS_FIELD
ConstantVariable,Class,Postapibyusername,Variable,HAS_FIELD
ConstantVariable,Class,Postdeleteapilistuser,Variable,HAS_FIELD
ConstantVariable,Class,Apilist,Variable,HAS_FIELD
ConstantVariable,Class,Genusergetapi,Variable,HAS_FIELD
ConstantVariable,Class,Genusergetapiuser,Variable,HAS_FIELD
ConstantVariable,Class,Adminapiaccess,Variable,HAS_FIELD
ConstantVariable,Class,Apiaccesstoall,Variable,HAS_FIELD
ConstantVariable,Class,Apiaccesstoalllist,Variable,HAS_FIELD
ConstantVariable,Class,Apiaccesstoallwithauth,Variable,HAS_FIELD
ConstantVariable,Class,Apiaccesstoallwithauthlist,Variable,HAS_FIELD
ConstantVariable,Class,Adminacessapi,Variable,HAS_FIELD
ConstantVariable,Class,getPreviousDate,Method,DECLARES
getPreviousDate,Method,Date,Variable,USES
getPreviousDate,Method,Daysago,Variable,USES
ConstantVariable,Class,timestamp,Method,DECLARES
timestamp,Method,Ctx,Variable,USES
timestamp,Method,Almrepo,Variable,USES
timestamp,Method,Almconfig,Variable,USES
timestamp,Method,Timezone,Variable,USES
timestamp,Method,Zoneutc,Variable,USES
timestamp,Method,Dt,Variable,USES
ConstantVariable,Class,getLastRun,Method,DECLARES
getLastRun,Method,Mainmodel,Variable,USES
getLastRun,Method,Mailsetuprepo,Variable,USES
getLastRun,Method,Repo,Variable,USES
getLastRun,Method,Resultval,Variable,USES
getLastRun,Method,Tempmapname,Variable,USES
getLastRun,Method,Mapname,Variable,USES
getLastRun,Method,Srset,Variable,USES
ProjectCollector,Class,Ctx,Variable,HAS_FIELD
ProjectCollector,Class,Projectname,Variable,HAS_FIELD
ProjectCollector,Class,Executor,Variable,HAS_FIELD
ProjectCollector,Class,Threadgroup,Variable,HAS_FIELD
ProjectCollector,Class,Childthreadgroup,Variable,HAS_FIELD
ProjectCollector,Class,Log,Variable,HAS_FIELD
ProjectCollector,Class,Mailsent,Variable,HAS_FIELD
ProjectCollector,Class,Metric1,Variable,HAS_FIELD
ProjectCollector,Class,Toaddress,Variable,HAS_FIELD
ProjectCollector,Class,Msgbody,Variable,HAS_FIELD
ProjectCollector,Class,Subject,Variable,HAS_FIELD
ProjectCollector,Class,getProjectName,Method,DECLARES
ProjectCollector,Class,setProjectName,Method,DECLARES
setProjectName,Method,Projectname,Variable,USES
ProjectCollector,Class,printMessage,Method,DECLARES
ProjectCollector,Class,multiTaskThread,Method,DECLARES
multiTaskThread,Method,Projectname,Variable,USES
multiTaskThread,Method,Config,Variable,USES
multiTaskThread,Method,Portfoliorepo,Variable,USES
multiTaskThread,Method,Configs,Variable,USES
multiTaskThread,Method,Configurationrepo,Variable,USES
multiTaskThread,Method,Configurationcolection,Variable,USES
multiTaskThread,Method,Listoftools,Variable,USES
multiTaskThread,Method,Configuration1,Variable,USES
multiTaskThread,Method,Metric1,Variable,USES
ProjectCollector,Class,makeSwitchCaseCall,Method,DECLARES
makeSwitchCaseCall,Method,Threadgroup,Variable,USES
makeSwitchCaseCall,Method,Childthreadgroup,Variable,USES
ProjectCollector,Class,githubAction,Method,DECLARES
ProjectCollector,Class,servicenow,Method,DECLARES
Configuration,Class,Host,Variable,HAS_FIELD
Configuration,Class,Db,Variable,HAS_FIELD
Configuration,Class,Userid,Variable,HAS_FIELD
Configuration,Class,Password,Variable,HAS_FIELD
Configuration,Class,Port,Variable,HAS_FIELD
Configuration,Class,Secret,Variable,HAS_FIELD
DataConfig,Class,Log,Variable,HAS_FIELD
DataConfig,Class,Myobj,Variable,HAS_FIELD
DataConfig,Class,Mongotemplate,Variable,HAS_FIELD
DataConfig,Class,Ctx,Variable,HAS_FIELD
DataConfig,Class,Host,Variable,HAS_FIELD
DataConfig,Class,Port,Variable,HAS_FIELD
DataConfig,Class,Dbuserid,Variable,HAS_FIELD
DataConfig,Class,Db,Variable,HAS_FIELD
DataConfig,Class,Dbpassword,Variable,HAS_FIELD
DataConfig,Class,Secret,Variable,HAS_FIELD
DataConfig,Class,Client,Variable,HAS_FIELD
DataConfig,Class,In,Variable,HAS_FIELD
DataConfig,Class,getInstance,Method,DECLARES
getInstance,Method,Myobj,Variable,USES
DataConfig,Class,getDatabaseName,Method,DECLARES
DataConfig,Class,mongo,Method,DECLARES
mongo,Method,Properties,Variable,USES
mongo,Method,In,Variable,USES
mongo,Method,Host,Variable,USES
mongo,Method,Port,Variable,USES
mongo,Method,Dbuserid,Variable,USES
mongo,Method,Dbpassword,Variable,USES
mongo,Method,Secret,Variable,USES
mongo,Method,Db,Variable,USES
mongo,Method,Mongocredential,Variable,USES
mongo,Method,Client,Variable,USES
DataConfig,Class,getMappingBasePackage,Method,DECLARES
DataConfig,Class,mongoTemplate,Method,DECLARES
mongoTemplate,Method,Mongotemplate,Variable,USES
DataConfig,Class,getContext,Method,DECLARES
getContext,Method,Ctx,Variable,USES
MongoAggregate,Class,Template,Variable,HAS_FIELD
MongoAggregate,Class,Logger,Variable,HAS_FIELD
MongoAggregate,Class,Metricrepo,Variable,HAS_FIELD
MongoAggregate,Class,Efforthistoryrepo,Variable,HAS_FIELD
MongoAggregate,Class,aggregateMetrics,Method,DECLARES
aggregateMetrics,Method,Metricrepo,Variable,USES
aggregateMetrics,Method,Transitionrepo,Variable,USES
aggregateMetrics,Method,Efforthistoryrepo,Variable,USES
aggregateMetrics,Method,Iterationrepo,Variable,USES
aggregateMetrics,Method,Metriclist,Variable,USES
aggregateMetrics,Method,Transitionlist,Variable,USES
aggregateMetrics,Method,Transitiongrouped,Variable,USES
aggregateMetrics,Method,Efforthistorylist,Variable,USES
aggregateMetrics,Method,Effortsgrouped,Variable,USES
aggregateMetrics,Method,Metricitr,Variable,USES
aggregateMetrics,Method,Aggregatedmetriclist,Variable,USES
aggregateMetrics,Method,Agr,Variable,USES
MongoAggregate,Class,aggregate,Method,DECLARES
aggregate,Method,Query,Variable,USES
aggregate,Method,Metricrepo,Variable,USES
aggregate,Method,Transitionrepo,Variable,USES
aggregate,Method,Efforthistoryrepo,Variable,USES
aggregate,Method,Iterationrepo,Variable,USES
aggregate,Method,Metriclist,Variable,USES
aggregate,Method,Transitionlist,Variable,USES
aggregate,Method,Transitiongrouped,Variable,USES
aggregate,Method,Efforthistorylist,Variable,USES
aggregate,Method,Effortsgrouped,Variable,USES
aggregate,Method,Metricitr,Variable,USES
aggregate,Method,Aggregatedmetriclist,Variable,USES
aggregate,Method,Aggregatedmetriclistgrouped,Variable,USES
aggregate,Method,Iterationlist,Variable,USES
aggregate,Method,Iterationirt,Variable,USES
aggregate,Method,Aggreagation,Variable,USES
aggregate,Method,Iteraiotn,Variable,USES
aggregate,Method,Aggregationitr,Variable,USES
aggregate,Method,Agr,Variable,USES
ALMConfiguration,Class,Storyname,Variable,HAS_FIELD
ALMConfiguration,Class,Priorityname,Variable,HAS_FIELD
ALMConfiguration,Class,Projectname,Variable,HAS_FIELD
ALMConfiguration,Class,Defectname,Variable,HAS_FIELD
ALMConfiguration,Class,Releasename,Variable,HAS_FIELD
ALMConfiguration,Class,Taskname,Variable,HAS_FIELD
ALMConfiguration,Class,Closestate,Variable,HAS_FIELD
ALMConfiguration,Class,Progressstate,Variable,HAS_FIELD
ALMConfiguration,Class,Newstate,Variable,HAS_FIELD
ALMConfiguration,Class,Criticalpriority,Variable,HAS_FIELD
ALMConfiguration,Class,Highpriority,Variable,HAS_FIELD
ALMConfiguration,Class,Medpriority,Variable,HAS_FIELD
ALMConfiguration,Class,Lowpriority,Variable,HAS_FIELD
ALMConfiguration,Class,Tracksset,Variable,HAS_FIELD
ALMConfiguration,Class,Rejectionphase,Variable,HAS_FIELD
ALMConfiguration,Class,Reopenphase,Variable,HAS_FIELD
ALMConfiguration,Class,Testingphase,Variable,HAS_FIELD
ALMConfiguration,Class,Productionphase,Variable,HAS_FIELD
ALMConfiguration,Class,Personhours,Variable,HAS_FIELD
ALMConfiguration,Class,Timezone,Variable,HAS_FIELD
ALMConfiguration,Class,Velocityfields,Variable,HAS_FIELD
ALMConfiguration,Class,Environment,Variable,HAS_FIELD
ALMConfiguration,Class,Safeenabled,Variable,HAS_FIELD
ALMConfiguration,Class,Fixedstate,Variable,HAS_FIELD
ALMConfiguration,Class,Ccrlabel,Variable,HAS_FIELD
ALMConfiguration,Class,Cycletimestates,Variable,HAS_FIELD
ALMConfiguration,Class,Throughputstates,Variable,HAS_FIELD
ALMConfiguration,Class,Filteredsprints,Variable,HAS_FIELD
ALMConfiguration,Class,Issuelinktypes,Variable,HAS_FIELD
ALMConfiguration,Class,Firstsprint,Variable,HAS_FIELD
ALMConfiguration,Class,getCcrLabel,Method,DECLARES
ALMConfiguration,Class,setCcrLabel,Method,DECLARES
setCcrLabel,Method,Ccrlabel,Variable,USES
ALMConfiguration,Class,getCycleTimeStates,Method,DECLARES
ALMConfiguration,Class,setCycleTimeStates,Method,DECLARES
setCycleTimeStates,Method,Cycletimestates,Variable,USES
ALMConfiguration,Class,getThroughputStates,Method,DECLARES
ALMConfiguration,Class,setThroughputStates,Method,DECLARES
setThroughputStates,Method,Throughputstates,Variable,USES
ALMConfiguration,Class,getRejectionPhase,Method,DECLARES
ALMConfiguration,Class,setRejectionPhase,Method,DECLARES
setRejectionPhase,Method,Rejectionphase,Variable,USES
ALMConfiguration,Class,getReopenPhase,Method,DECLARES
ALMConfiguration,Class,setReopenPhase,Method,DECLARES
setReopenPhase,Method,Reopenphase,Variable,USES
ALMConfiguration,Class,getTestingPhase,Method,DECLARES
ALMConfiguration,Class,setTestingPhase,Method,DECLARES
setTestingPhase,Method,Testingphase,Variable,USES
ALMConfiguration,Class,getProductionPhase,Method,DECLARES
ALMConfiguration,Class,setProductionPhase,Method,DECLARES
setProductionPhase,Method,Productionphase,Variable,USES
ALMConfiguration,Class,getStoryName,Method,DECLARES
ALMConfiguration,Class,setStoryName,Method,DECLARES
setStoryName,Method,Storyname,Variable,USES
ALMConfiguration,Class,getPriorityName,Method,DECLARES
ALMConfiguration,Class,setPriorityName,Method,DECLARES
setPriorityName,Method,Priorityname,Variable,USES
ALMConfiguration,Class,getReleaseName,Method,DECLARES
ALMConfiguration,Class,setReleaseName,Method,DECLARES
setReleaseName,Method,Releasename,Variable,USES
ALMConfiguration,Class,getTaskName,Method,DECLARES
ALMConfiguration,Class,setTaskName,Method,DECLARES
setTaskName,Method,Taskname,Variable,USES
ALMConfiguration,Class,getCloseState,Method,DECLARES
ALMConfiguration,Class,setCloseState,Method,DECLARES
setCloseState,Method,Closestate,Variable,USES
ALMConfiguration,Class,getCriticalPriority,Method,DECLARES
ALMConfiguration,Class,setCriticalPriority,Method,DECLARES
setCriticalPriority,Method,Criticalpriority,Variable,USES
ALMConfiguration,Class,getHighPriority,Method,DECLARES
ALMConfiguration,Class,setHighPriority,Method,DECLARES
setHighPriority,Method,Highpriority,Variable,USES
ALMConfiguration,Class,getMedPriority,Method,DECLARES
ALMConfiguration,Class,setMedPriority,Method,DECLARES
setMedPriority,Method,Medpriority,Variable,USES
ALMConfiguration,Class,getLowPriority,Method,DECLARES
ALMConfiguration,Class,setLowPriority,Method,DECLARES
setLowPriority,Method,Lowpriority,Variable,USES
ALMConfiguration,Class,getProjectName,Method,DECLARES
ALMConfiguration,Class,setProjectName,Method,DECLARES
ALMConfiguration,Class,getDefectName,Method,DECLARES
ALMConfiguration,Class,setDefectName,Method,DECLARES
setDefectName,Method,Defectname,Variable,USES
ALMConfiguration,Class,getTrendType,Method,DECLARES
ALMConfiguration,Class,setTrendType,Method,DECLARES
setTrendType,Method,Trendtype,Variable,USES
ALMConfiguration,Class,getTracksSet,Method,DECLARES
ALMConfiguration,Class,setTracksSet,Method,DECLARES
setTracksSet,Method,Tracksset,Variable,USES
ALMConfiguration,Class,getPersonHours,Method,DECLARES
ALMConfiguration,Class,setPersonHours,Method,DECLARES
setPersonHours,Method,Personhours,Variable,USES
ALMConfiguration,Class,getTimeZone,Method,DECLARES
ALMConfiguration,Class,setTimeZone,Method,DECLARES
setTimeZone,Method,Timezone,Variable,USES
ALMConfiguration,Class,getVelocityFields,Method,DECLARES
ALMConfiguration,Class,setVelocityFields,Method,DECLARES
setVelocityFields,Method,Velocityfields,Variable,USES
ALMConfiguration,Class,getEnvironment,Method,DECLARES
ALMConfiguration,Class,setEnvironment,Method,DECLARES
setEnvironment,Method,Environment,Variable,USES
ALMConfiguration,Class,isSafeEnabled,Method,DECLARES
ALMConfiguration,Class,setSafeEnabled,Method,DECLARES
setSafeEnabled,Method,Safeenabled,Variable,USES
ALMConfiguration,Class,getProgressState,Method,DECLARES
ALMConfiguration,Class,setProgressState,Method,DECLARES
setProgressState,Method,Progressstate,Variable,USES
ALMConfiguration,Class,getNewState,Method,DECLARES
ALMConfiguration,Class,setNewState,Method,DECLARES
setNewState,Method,Newstate,Variable,USES
ALMConfiguration,Class,getFixedState,Method,DECLARES
ALMConfiguration,Class,setFixedState,Method,DECLARES
setFixedState,Method,Fixedstate,Variable,USES
ALMConfiguration,Class,getFilteredSprints,Method,DECLARES
ALMConfiguration,Class,setFilteredSprints,Method,DECLARES
setFilteredSprints,Method,Filteredsprints,Variable,USES
ALMConfiguration,Class,getIssueLinkTypes,Method,DECLARES
ALMConfiguration,Class,setIssueLinkTypes,Method,DECLARES
setIssueLinkTypes,Method,Issuelinktypes,Variable,USES
ALMConfiguration,Class,getFirstSprint,Method,DECLARES
ALMConfiguration,Class,setFirstSprint,Method,DECLARES
setFirstSprint,Method,Firstsprint,Variable,USES
BaseModel,Class,Id,Variable,HAS_FIELD
BaseModel,Class,getId,Method,DECLARES
BaseModel,Class,setId,Method,DECLARES
setId,Method,Id,Variable,USES
ChangeHistoryModel,Class,Changeitems,Table,MAPS_TO
ChangeHistoryModel,Class,Wid,Variable,HAS_FIELD
ChangeHistoryModel,Class,Field,Variable,HAS_FIELD
ChangeHistoryModel,Class,Oldvalue,Variable,HAS_FIELD
ChangeHistoryModel,Class,Newvalue,Variable,HAS_FIELD
ChangeHistoryModel,Class,Date,Variable,HAS_FIELD
ChangeHistoryModel,Class,Projkey,Variable,HAS_FIELD
ChangeHistoryModel,Class,Pname,Variable,HAS_FIELD
ChangeHistoryModel,Class,getProjKey,Method,DECLARES
ChangeHistoryModel,Class,setProjKey,Method,DECLARES
setProjKey,Method,Projkey,Variable,USES
ChangeHistoryModel,Class,getProjectName,Method,DECLARES
ChangeHistoryModel,Class,setProjectName,Method,DECLARES
setProjectName,Method,Pname,Variable,USES
ChangeHistoryModel,Class,getwId,Method,DECLARES
ChangeHistoryModel,Class,setwId,Method,DECLARES
setwId,Method,Wid,Variable,USES
ChangeHistoryModel,Class,getField,Method,DECLARES
ChangeHistoryModel,Class,setField,Method,DECLARES
setField,Method,Field,Variable,USES
ChangeHistoryModel,Class,getOldValue,Method,DECLARES
ChangeHistoryModel,Class,setOldValue,Method,DECLARES
setOldValue,Method,Oldvalue,Variable,USES
ChangeHistoryModel,Class,getNewValue,Method,DECLARES
ChangeHistoryModel,Class,setNewValue,Method,DECLARES
setNewValue,Method,Newvalue,Variable,USES
ChangeHistoryModel,Class,getDate,Method,DECLARES
ChangeHistoryModel,Class,setDate,Method,DECLARES
setDate,Method,Date,Variable,USES
ComponentVelocityList,Class,Component,Variable,HAS_FIELD
ComponentVelocityList,Class,Velocitylist,Variable,HAS_FIELD
ComponentVelocityList,Class,getComponent,Method,DECLARES
ComponentVelocityList,Class,setComponent,Method,DECLARES
setComponent,Method,Component,Variable,USES
ComponentVelocityList,Class,getVelocityList,Method,DECLARES
ComponentVelocityList,Class,setVelocityList,Method,DECLARES
ConfigurationSetting,Class,Timestamp,Variable,HAS_FIELD
ConfigurationSetting,Class,Baseline,Variable,HAS_FIELD
ConfigurationSetting,Class,Projectname,Variable,HAS_FIELD
ConfigurationSetting,Class,Addflag,Variable,HAS_FIELD
ConfigurationSetting,Class,Projecttype,Variable,HAS_FIELD
ConfigurationSetting,Class,Manualdata,Variable,HAS_FIELD
ConfigurationSetting,Class,Metric,Variable,HAS_FIELD
ConfigurationSetting,Class,isManualData,Method,DECLARES
ConfigurationSetting,Class,setManualData,Method,DECLARES
setManualData,Method,Manualdata,Variable,USES
ConfigurationSetting,Class,getTimeStamp,Method,DECLARES
ConfigurationSetting,Class,setTimeStamp,Method,DECLARES
setTimeStamp,Method,Timestamp,Variable,USES
ConfigurationSetting,Class,getProjectName,Method,DECLARES
ConfigurationSetting,Class,setProjectName,Method,DECLARES
ConfigurationSetting,Class,getMetrics,Method,DECLARES
ConfigurationSetting,Class,isAddFlag,Method,DECLARES
ConfigurationSetting,Class,setAddFlag,Method,DECLARES
setAddFlag,Method,Addflag,Variable,USES
ConfigurationSetting,Class,isBaseline,Method,DECLARES
ConfigurationSetting,Class,setBaseLine,Method,DECLARES
setBaseLine,Method,Baseline,Variable,USES
ConfigurationSetting,Class,getProjectType,Method,DECLARES
ConfigurationSetting,Class,setProjectType,Method,DECLARES
setProjectType,Method,Projecttype,Variable,USES
ConfigurationToolInfoMetric,Class,Selected,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Id,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Toolname,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Url,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Username,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Password,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Tooltype,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Widgetname,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Jobname,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Projectcode,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Domain,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Host,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Port,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Dbtype,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Schema,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Reponame,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Secret,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,Manualdata,Variable,HAS_FIELD
ConfigurationToolInfoMetric,Class,getId,Method,DECLARES
ConfigurationToolInfoMetric,Class,setId,Method,DECLARES
ConfigurationToolInfoMetric,Class,getToolName,Method,DECLARES
ConfigurationToolInfoMetric,Class,setToolName,Method,DECLARES
setToolName,Method,Toolname,Variable,USES
ConfigurationToolInfoMetric,Class,getURL,Method,DECLARES
ConfigurationToolInfoMetric,Class,setURL,Method,DECLARES
setURL,Method,Url,Variable,USES
ConfigurationToolInfoMetric,Class,getUserName,Method,DECLARES
ConfigurationToolInfoMetric,Class,setUserName,Method,DECLARES
setUserName,Method,Username,Variable,USES
ConfigurationToolInfoMetric,Class,getPassword,Method,DECLARES
ConfigurationToolInfoMetric,Class,setPassword,Method,DECLARES
setPassword,Method,Password,Variable,USES
ConfigurationToolInfoMetric,Class,getToolType,Method,DECLARES
ConfigurationToolInfoMetric,Class,setToolType,Method,DECLARES
setToolType,Method,Tooltype,Variable,USES
ConfigurationToolInfoMetric,Class,getWidgetName,Method,DECLARES
ConfigurationToolInfoMetric,Class,setWidgetName,Method,DECLARES
setWidgetName,Method,Widgetname,Variable,USES
ConfigurationToolInfoMetric,Class,getJobName,Method,DECLARES
ConfigurationToolInfoMetric,Class,setJobName,Method,DECLARES
setJobName,Method,Jobname,Variable,USES
ConfigurationToolInfoMetric,Class,getProjectCode,Method,DECLARES
ConfigurationToolInfoMetric,Class,setProjectCode,Method,DECLARES
setProjectCode,Method,Projectcode,Variable,USES
ConfigurationToolInfoMetric,Class,getSelected,Method,DECLARES
ConfigurationToolInfoMetric,Class,setSelected,Method,DECLARES
setSelected,Method,Selected,Variable,USES
ConfigurationToolInfoMetric,Class,getDomain,Method,DECLARES
ConfigurationToolInfoMetric,Class,setDomain,Method,DECLARES
setDomain,Method,Domain,Variable,USES
ConfigurationToolInfoMetric,Class,getHost,Method,DECLARES
ConfigurationToolInfoMetric,Class,setHost,Method,DECLARES
setHost,Method,Host,Variable,USES
ConfigurationToolInfoMetric,Class,getPort,Method,DECLARES
ConfigurationToolInfoMetric,Class,setPort,Method,DECLARES
setPort,Method,Port,Variable,USES
ConfigurationToolInfoMetric,Class,getDbType,Method,DECLARES
ConfigurationToolInfoMetric,Class,setDbType,Method,DECLARES
setDbType,Method,Dbtype,Variable,USES
ConfigurationToolInfoMetric,Class,getSchema,Method,DECLARES
ConfigurationToolInfoMetric,Class,setSchema,Method,DECLARES
setSchema,Method,Schema,Variable,USES
ConfigurationToolInfoMetric,Class,getRepoName,Method,DECLARES
ConfigurationToolInfoMetric,Class,setRepoName,Method,DECLARES
setRepoName,Method,Reponame,Variable,USES
ConfigurationToolInfoMetric,Class,getSecret,Method,DECLARES
ConfigurationToolInfoMetric,Class,setSecret,Method,DECLARES
setSecret,Method,Secret,Variable,USES
CustomFields,Class,Name,Variable,HAS_FIELD
CustomFields,Class,getName,Method,DECLARES
CustomFields,Class,setName,Method,DECLARES
setName,Method,Name,Variable,USES
CustomFields,Class,getValue,Method,DECLARES
CustomFields,Class,setValue,Method,DECLARES
IterationModel,Class,Timestamp,Variable,HAS_FIELD
IterationModel,Class,Sname,Variable,HAS_FIELD
IterationModel,Class,Stdate,Variable,HAS_FIELD
IterationModel,Class,Enddate,Variable,HAS_FIELD
IterationModel,Class,Velocity,Variable,HAS_FIELD
IterationModel,Class,Spplanned,Variable,HAS_FIELD
IterationModel,Class,Relname,Variable,HAS_FIELD
IterationModel,Class,Reldate,Variable,HAS_FIELD
IterationModel,Class,Techdebt,Variable,HAS_FIELD
IterationModel,Class,Teamsize,Variable,HAS_FIELD
IterationModel,Class,Ptestcase,Variable,HAS_FIELD
IterationModel,Class,Fadd,Variable,HAS_FIELD
IterationModel,Class,Fdel,Variable,HAS_FIELD
IterationModel,Class,Fmod,Variable,HAS_FIELD
IterationModel,Class,Totdefects,Variable,HAS_FIELD
IterationModel,Class,Totcloseddefects,Variable,HAS_FIELD
IterationModel,Class,Totopendefetct,Variable,HAS_FIELD
IterationModel,Class,Stpersprint,Variable,HAS_FIELD
IterationModel,Class,Buildfailed,Variable,HAS_FIELD
IterationModel,Class,State,Variable,HAS_FIELD
IterationModel,Class,Pname,Variable,HAS_FIELD
IterationModel,Class,Palmtype,Variable,HAS_FIELD
IterationModel,Class,Sid,Variable,HAS_FIELD
IterationModel,Class,Completeddate,Variable,HAS_FIELD
IterationModel,Class,Totalstorypoints,Variable,HAS_FIELD
IterationModel,Class,Buildfail,Variable,HAS_FIELD
IterationModel,Class,Projkey,Variable,HAS_FIELD
IterationModel,Class,getTimeStamp,Method,DECLARES
IterationModel,Class,setTimeStamp,Method,DECLARES
IterationModel,Class,getTotClosedDefects,Method,DECLARES
IterationModel,Class,setTotClosedDefects,Method,DECLARES
setTotClosedDefects,Method,Totcloseddefects,Variable,USES
IterationModel,Class,getTotOpenDefetct,Method,DECLARES
IterationModel,Class,setTotOpenDefetct,Method,DECLARES
setTotOpenDefetct,Method,Totopendefetct,Variable,USES
IterationModel,Class,setTotDefects,Method,DECLARES
setTotDefects,Method,Totdefects,Variable,USES
IterationModel,Class,getsName,Method,DECLARES
IterationModel,Class,setsName,Method,DECLARES
setsName,Method,Sname,Variable,USES
IterationOutModel,Class,Timestamp,Variable,HAS_FIELD
IterationOutModel,Class,Sname,Variable,HAS_FIELD
IterationOutModel,Class,Stdate,Variable,HAS_FIELD
IterationOutModel,Class,Enddate,Variable,HAS_FIELD
IterationOutModel,Class,Velocity,Variable,HAS_FIELD
IterationOutModel,Class,Spplanned,Variable,HAS_FIELD
IterationOutModel,Class,Relname,Variable,HAS_FIELD
IterationOutModel,Class,Reldate,Variable,HAS_FIELD
IterationOutModel,Class,Techdebt,Variable,HAS_FIELD
IterationOutModel,Class,Teamsize,Variable,HAS_FIELD
IterationOutModel,Class,Ptestcase,Variable,HAS_FIELD
IterationOutModel,Class,Fadd,Variable,HAS_FIELD
IterationOutModel,Class,Fdel,Variable,HAS_FIELD
IterationOutModel,Class,Fmod,Variable,HAS_FIELD
IterationOutModel,Class,Totdefects,Variable,HAS_FIELD
IterationOutModel,Class,Totcloseddefects,Variable,HAS_FIELD
IterationOutModel,Class,Totopendefetct,Variable,HAS_FIELD
IterationOutModel,Class,Stpersprint,Variable,HAS_FIELD
IterationOutModel,Class,Buildfailed,Variable,HAS_FIELD
IterationOutModel,Class,State,Variable,HAS_FIELD
IterationOutModel,Class,Pname,Variable,HAS_FIELD
IterationOutModel,Class,Projkey,Variable,HAS_FIELD
IterationOutModel,Class,Palmtype,Variable,HAS_FIELD
IterationOutModel,Class,Sid,Variable,HAS_FIELD
IterationOutModel,Class,Completeddate,Variable,HAS_FIELD
IterationOutModel,Class,Totalstorypoints,Variable,HAS_FIELD
IterationOutModel,Class,Buildfail,Variable,HAS_FIELD
IterationOutModel,Class,Closedstories,Variable,HAS_FIELD
IterationOutModel,Class,Metrics,Variable,HAS_FIELD
IterationOutModel,Class,getClosedStories,Method,DECLARES
IterationOutModel,Class,setClosedStories,Method,DECLARES
IterationOutModel,Class,getTimeStamp,Method,DECLARES
IterationOutModel,Class,setTimeStamp,Method,DECLARES
IterationOutModel,Class,getsName,Method,DECLARES
IterationOutModel,Class,setsName,Method,DECLARES
IterationOutModel,Class,getStDate,Method,DECLARES
IterationOutModel,Class,setStDate,Method,DECLARES
IterationOutModel,Class,getEndDate,Method,DECLARES
IterationOutModel,Class,setEndDate,Method,DECLARES
IterationOutModel,Class,getVelocity,Method,DECLARES
IterationOutModel,Class,getsPPlanned,Method,DECLARES
IterationOutModel,Class,setsPPlanned,Method,DECLARES
IterationOutModel,Class,getRelDate,Method,DECLARES
IterationOutModel,Class,setRelDate,Method,DECLARES
IterationOutModel,Class,getTotClosedDefects,Method,DECLARES
IterationOutModel,Class,setTotClosedDefects,Method,DECLARES
IterationOutModel,Class,getTotOpenDefetct,Method,DECLARES
IterationOutModel,Class,setTotOpenDefetct,Method,DECLARES
IterationOutModel,Class,getTotalStoryPoints,Method,DECLARES
IterationOutModel,Class,setTotalStoryPoints,Method,DECLARES
IterationOutModel,Class,getBuildFail,Method,DECLARES
IterationOutModel,Class,setBuildFail,Method,DECLARES
IterationOutModel,Class,setVelocity,Method,DECLARES
IterationOutModel,Class,setTotDefects,Method,DECLARES
IterationOutModel,Class,getTechDebt,Method,DECLARES
IterationOutModel,Class,setTechDebt,Method,DECLARES
IterationOutModel,Class,getTeamsize,Method,DECLARES
IterationOutModel,Class,setTeamSize,Method,DECLARES
IterationOutModel,Class,getpTestCase,Method,DECLARES
IterationOutModel,Class,setpTestCase,Method,DECLARES
IterationOutModel,Class,getfAdd,Method,DECLARES
IterationOutModel,Class,setfAdd,Method,DECLARES
IterationOutModel,Class,getfDel,Method,DECLARES
IterationOutModel,Class,setfDel,Method,DECLARES
IterationOutModel,Class,getfMod,Method,DECLARES
IterationOutModel,Class,setfMod,Method,DECLARES
IterationOutModel,Class,getTotDefects,Method,DECLARES
IterationOutModel,Class,getStPerSprint,Method,DECLARES
IterationOutModel,Class,setStPerSprint,Method,DECLARES
IterationOutModel,Class,getBuildFailed,Method,DECLARES
IterationOutModel,Class,setBuildFailed,Method,DECLARES
IterationOutModel,Class,getState,Method,DECLARES
IterationOutModel,Class,setState,Method,DECLARES
IterationOutModel,Class,getpName,Method,DECLARES
IterationOutModel,Class,setpName,Method,DECLARES
IterationOutModel,Class,getpAlmType,Method,DECLARES
IterationOutModel,Class,setpAlmType,Method,DECLARES
IterationOutModel,Class,getsId,Method,DECLARES
IterationOutModel,Class,setsId,Method,DECLARES
IterationOutModel,Class,getCompletedDate,Method,DECLARES
IterationOutModel,Class,setCompletedDate,Method,DECLARES
IterationOutModel,Class,getMetrics,Method,DECLARES
IterationOutModel,Class,setMetrics,Method,DECLARES
IterationOutModel,Class,getRelName,Method,DECLARES
IterationOutModel,Class,setRelName,Method,DECLARES
IterationOutModel,Class,getProjKey,Method,DECLARES
IterationOutModel,Class,setProjKey,Method,DECLARES
setClosedStories,Method,Closedstories,Variable,USES
setStDate,Method,Stdate,Variable,USES
setEndDate,Method,Enddate,Variable,USES
setsPPlanned,Method,Spplanned,Variable,USES
setRelDate,Method,Reldate,Variable,USES
setTotalStoryPoints,Method,Totalstorypoints,Variable,USES
setBuildFail,Method,Buildfail,Variable,USES
setVelocity,Method,Velocity,Variable,USES
setTechDebt,Method,Techdebt,Variable,USES
setTeamSize,Method,Teamsize,Variable,USES
setpTestCase,Method,Ptestcase,Variable,USES
setfAdd,Method,Fadd,Variable,USES
setfDel,Method,Fdel,Variable,USES
setfMod,Method,Fmod,Variable,USES
setStPerSprint,Method,Stpersprint,Variable,USES
setBuildFailed,Method,Buildfailed,Variable,USES
setState,Method,State,Variable,USES
setpName,Method,Pname,Variable,USES
setpAlmType,Method,Palmtype,Variable,USES
setsId,Method,Sid,Variable,USES
setCompletedDate,Method,Completeddate,Variable,USES
setMetrics,Method,Metrics,Variable,USES
setRelName,Method,Relname,Variable,USES
MetricsModel,Class,Actest,Variable,HAS_FIELD
MetricsModel,Class,Affectedversions,Variable,HAS_FIELD
MetricsModel,Class,Allocateddate,Variable,HAS_FIELD
MetricsModel,Class,Assgnto,Variable,HAS_FIELD
MetricsModel,Class,Baseline,Variable,HAS_FIELD
MetricsModel,Class,Components,Variable,HAS_FIELD
MetricsModel,Class,Createdate,Variable,HAS_FIELD
MetricsModel,Class,Cycletime,Variable,HAS_FIELD
MetricsModel,Class,Defectinjector,Variable,HAS_FIELD
MetricsModel,Class,Donedate,Variable,HAS_FIELD
MetricsModel,Class,Effort,Variable,HAS_FIELD
MetricsModel,Class,Epiclink,Variable,HAS_FIELD
MetricsModel,Class,Estchange,Variable,HAS_FIELD
MetricsModel,Class,Exteffort,Variable,HAS_FIELD
MetricsModel,Class,Fixver,Variable,HAS_FIELD
MetricsModel,Class,Inwardissuelink,Variable,HAS_FIELD
MetricsModel,Class,Leadtime,Variable,HAS_FIELD
MetricsModel,Class,Orgest,Variable,HAS_FIELD
MetricsModel,Class,Outwardissuelink,Variable,HAS_FIELD
MetricsModel,Class,Epicissues,Variable,HAS_FIELD
MetricsModel,Class,Palmtype,Variable,HAS_FIELD
MetricsModel,Class,Pname,Variable,HAS_FIELD
MetricsModel,Class,Priority,Variable,HAS_FIELD
MetricsModel,Class,Remtime,Variable,HAS_FIELD
MetricsModel,Class,Resdate,Variable,HAS_FIELD
MetricsModel,Class,Severity,Variable,HAS_FIELD
MetricsModel,Class,Sid,Variable,HAS_FIELD
MetricsModel,Class,Multisprints,Variable,HAS_FIELD
MetricsModel,Class,Sname,Variable,HAS_FIELD
MetricsModel,Class,State,Variable,HAS_FIELD
MetricsModel,Class,Stateset,Variable,HAS_FIELD
MetricsModel,Class,Statuscategory,Variable,HAS_FIELD
MetricsModel,Class,Storypoints,Variable,HAS_FIELD
MetricsModel,Class,Subtasklist,Variable,HAS_FIELD
MetricsModel,Class,Summ,Variable,HAS_FIELD
MetricsModel,Class,Tasklist,Variable,HAS_FIELD
MetricsModel,Class,Type,Variable,HAS_FIELD
MetricsModel,Class,Updateddate,Variable,HAS_FIELD
MetricsModel,Class,Upddate,Variable,HAS_FIELD
MetricsModel,Class,Waittime,Variable,HAS_FIELD
MetricsModel,Class,Wid,Variable,HAS_FIELD
MetricsModel,Class,Targetrelease,Variable,HAS_FIELD
MetricsModel,Class,Projkey,Variable,HAS_FIELD
MetricsModel,Class,Url,Variable,HAS_FIELD
MetricsModel,Class,Squads,Variable,HAS_FIELD
MetricsModel,Class,Category,Variable,HAS_FIELD
MetricsModel,Class,Whenfound,Variable,HAS_FIELD
MetricsModel,Class,Wherefound,Variable,HAS_FIELD
MetricsModel,Class,Howfound,Variable,HAS_FIELD
MetricsModel,Class,Environment,Variable,HAS_FIELD
MetricsModel,Class,Acfeatureorcapability,Variable,HAS_FIELD
MetricsModel,Class,Acfeatureid,Variable,HAS_FIELD
MetricsModel,Class,Rallyrefurl,Variable,HAS_FIELD
MetricsModel,Class,Customfields,Variable,HAS_FIELD
MonogOutMetrics,Class,getCustomFields,Method,DECLARES
MonogOutMetrics,Class,setCustomFields,Method,DECLARES
MonogOutMetrics,Class,Actest,Variable,HAS_FIELD
MonogOutMetrics,Class,Affectedversions,Variable,HAS_FIELD
MonogOutMetrics,Class,Allocateddate,Variable,HAS_FIELD
MonogOutMetrics,Class,Assgnto,Variable,HAS_FIELD
MonogOutMetrics,Class,Baseline,Variable,HAS_FIELD
MonogOutMetrics,Class,Components,Variable,HAS_FIELD
MonogOutMetrics,Class,Createdate,Variable,HAS_FIELD
MonogOutMetrics,Class,Cycletime,Variable,HAS_FIELD
MonogOutMetrics,Class,Defectinjector,Variable,HAS_FIELD
MonogOutMetrics,Class,Donedate,Variable,HAS_FIELD
MonogOutMetrics,Class,Effort,Variable,HAS_FIELD
MonogOutMetrics,Class,Efforts,Variable,HAS_FIELD
MonogOutMetrics,Class,Epiclink,Variable,HAS_FIELD
MonogOutMetrics,Class,Estchange,Variable,HAS_FIELD
MonogOutMetrics,Class,Exteffort,Variable,HAS_FIELD
MonogOutMetrics,Class,Fixver,Variable,HAS_FIELD
MonogOutMetrics,Class,Targetrelease,Variable,HAS_FIELD
MonogOutMetrics,Class,Inwardissuelink,Variable,HAS_FIELD
MonogOutMetrics,Class,Leadtime,Variable,HAS_FIELD
MonogOutMetrics,Class,Orgest,Variable,HAS_FIELD
MonogOutMetrics,Class,Outwardissuelink,Variable,HAS_FIELD
MonogOutMetrics,Class,Epicissues,Variable,HAS_FIELD
MonogOutMetrics,Class,Palmtype,Variable,HAS_FIELD
MonogOutMetrics,Class,Pname,Variable,HAS_FIELD
MonogOutMetrics,Class,Priority,Variable,HAS_FIELD
MonogOutMetrics,Class,Remtime,Variable,HAS_FIELD
MonogOutMetrics,Class,Resdate,Variable,HAS_FIELD
MonogOutMetrics,Class,Severity,Variable,HAS_FIELD
MonogOutMetrics,Class,Sid,Variable,HAS_FIELD
MonogOutMetrics,Class,Multisprints,Variable,HAS_FIELD
MonogOutMetrics,Class,Sname,Variable,HAS_FIELD
MonogOutMetrics,Class,State,Variable,HAS_FIELD
MonogOutMetrics,Class,Stateset,Variable,HAS_FIELD
MonogOutMetrics,Class,Statuscategory,Variable,HAS_FIELD
MonogOutMetrics,Class,Storypoints,Variable,HAS_FIELD
MonogOutMetrics,Class,Subtasklist,Variable,HAS_FIELD
MonogOutMetrics,Class,Summ,Variable,HAS_FIELD
MonogOutMetrics,Class,Tasklist,Variable,HAS_FIELD
MonogOutMetrics,Class,Transitions,Variable,HAS_FIELD
MonogOutMetrics,Class,Type,Variable,HAS_FIELD
MonogOutMetrics,Class,Updateddate,Variable,HAS_FIELD
MonogOutMetrics,Class,Upddate,Variable,HAS_FIELD
MonogOutMetrics,Class,Projkey,Variable,HAS_FIELD
MonogOutMetrics,Class,Label,Variable,HAS_FIELD
MonogOutMetrics,Class,Waittime,Variable,HAS_FIELD
MonogOutMetrics,Class,Wid,Variable,HAS_FIELD
MonogOutMetrics,Class,Squads,Variable,HAS_FIELD
MonogOutMetrics,Class,Category,Variable,HAS_FIELD
MonogOutMetrics,Class,Whenfound,Variable,HAS_FIELD
MonogOutMetrics,Class,Wherefound,Variable,HAS_FIELD
MonogOutMetrics,Class,Howfound,Variable,HAS_FIELD
MonogOutMetrics,Class,Environment,Variable,HAS_FIELD
MonogOutMetrics,Class,Acfeatureorcapability,Variable,HAS_FIELD
MonogOutMetrics,Class,Acfeatureid,Variable,HAS_FIELD
MonogOutMetrics,Class,Rallyrefurl,Variable,HAS_FIELD
MonogOutMetrics,Class,Customfields,Variable,HAS_FIELD
ScoreCardSprintData,Class,Sprintname,Variable,HAS_FIELD
ScoreCardSprintData,Class,Sprintid,Variable,HAS_FIELD
ScoreCardSprintData,Class,Commitedsp,Variable,HAS_FIELD
ScoreCardSprintData,Class,Completedsp,Variable,HAS_FIELD
ScoreCardSprintData,Class,Commitedaftersp,Variable,HAS_FIELD
ScoreCardSprintData,Class,Spilloversp,Variable,HAS_FIELD
ScoreCardSprintData,Class,Refinedsp,Variable,HAS_FIELD
ScoreCardSprintData,Class,Removedsp,Variable,HAS_FIELD
ScoreCardSprintData,Class,Startdate,Variable,HAS_FIELD
ScoreCardSprintData,Class,Enddate,Variable,HAS_FIELD
ScoreCardSprintData,Class,Effort,Variable,HAS_FIELD
ScoreCardSprintData,Class,Defects,Variable,HAS_FIELD
ScoreCardSprintData,Class,Capacity,Variable,HAS_FIELD
ScoreCardSprintData,Class,Committedstories,Variable,HAS_FIELD
ScoreCardSprintData,Class,Completedstories,Variable,HAS_FIELD
ScoreCardSprintData,Class,Refineddefects,Variable,HAS_FIELD
ScoreCardSprintData,Class,Completeddefects,Variable,HAS_FIELD
ScoreCardSprintData,Class,State,Variable,HAS_FIELD
ScoreCardSprintData,Class,Issuescommited,Variable,HAS_FIELD
ScoreCardSprintData,Class,Issuescommitedafter,Variable,HAS_FIELD
ScoreCardSprintData,Class,Issuescomplted,Variable,HAS_FIELD
ScoreCardSprintData,Class,Issuesremoved,Variable,HAS_FIELD
ScoreCardSprintData,Class,Issuesrefined,Variable,HAS_FIELD
ScoreCardSprintData,Class,Issuespillover,Variable,HAS_FIELD
ScoreCardSprintData,Class,Originalestimate,Variable,HAS_FIELD
ScoreCardSprintData,Class,Timelogged,Variable,HAS_FIELD
ScoreCardSprintData,Class,getOriginalEstimate,Method,DECLARES
ScoreCardSprintData,Class,setOriginalEstimate,Method,DECLARES
setOriginalEstimate,Method,Originalestimate,Variable,USES
ScoreCardSprintData,Class,getTimeLogged,Method,DECLARES
ScoreCardSprintData,Class,setTimeLogged,Method,DECLARES
setTimeLogged,Method,Timelogged,Variable,USES
ScoreCardSprintData,Class,getCommittedStories,Method,DECLARES
ScoreCardSprintData,Class,setCommittedStories,Method,DECLARES
setCommittedStories,Method,Committedstories,Variable,USES
ScoreCardSprintData,Class,getCompletedStories,Method,DECLARES
ScoreCardSprintData,Class,setCompletedStories,Method,DECLARES
setCompletedStories,Method,Completedstories,Variable,USES
ScoreCardSprintData,Class,getEffort,Method,DECLARES
ScoreCardSprintData,Class,setEffort,Method,DECLARES
setEffort,Method,Effort,Variable,USES
ScoreCardSprintData,Class,getDefects,Method,DECLARES
ScoreCardSprintData,Class,setDefects,Method,DECLARES
setDefects,Method,Defects,Variable,USES
ScoreCardSprintData,Class,getCapacity,Method,DECLARES
ScoreCardSprintData,Class,setCapacity,Method,DECLARES
setCapacity,Method,Capacity,Variable,USES
ScoreCardSprintData,Class,getCompletedDefects,Method,DECLARES
ScoreCardSprintData,Class,setCompletedDefects,Method,DECLARES
setCompletedDefects,Method,Completeddefects,Variable,USES
ScoreCardSprintData,Class,getStartDate,Method,DECLARES
ScoreCardSprintData,Class,setStartDate,Method,DECLARES
setStartDate,Method,Startdate,Variable,USES
ScoreCardSprintData,Class,getEndDate,Method,DECLARES
ScoreCardSprintData,Class,setEndDate,Method,DECLARES
ScoreCardSprintData,Class,getCommitedSp,Method,DECLARES
ScoreCardSprintData,Class,setCommitedSp,Method,DECLARES
setCommitedSp,Method,Commitedsp,Variable,USES
ScoreCardSprintData,Class,getCompletedSp,Method,DECLARES
ScoreCardSprintData,Class,setCompletedSp,Method,DECLARES
setCompletedSp,Method,Completedsp,Variable,USES
ScoreCardSprintData,Class,getCommitedAfterSp,Method,DECLARES
ScoreCardSprintData,Class,setCommitedAfterSp,Method,DECLARES
setCommitedAfterSp,Method,Commitedaftersp,Variable,USES
ScoreCardSprintData,Class,getRefinedSp,Method,DECLARES
ScoreCardSprintData,Class,setRefinedSp,Method,DECLARES
setRefinedSp,Method,Refinedsp,Variable,USES
ScoreCardSprintData,Class,getRemovedSp,Method,DECLARES
ScoreCardSprintData,Class,setRemovedSp,Method,DECLARES
setRemovedSp,Method,Removedsp,Variable,USES
ScoreCardSprintData,Class,getSprintName,Method,DECLARES
ScoreCardSprintData,Class,getIssuesCommited,Method,DECLARES
ScoreCardSprintData,Class,setIssuesCommited,Method,DECLARES
setIssuesCommited,Method,Issuescommited,Variable,USES
ScoreCardSprintData,Class,getIssuesCommitedAfter,Method,DECLARES
ScoreCardSprintData,Class,setIssuesCommitedAfter,Method,DECLARES
setIssuesCommitedAfter,Method,Issuescommitedafter,Variable,USES
ScoreCardSprintData,Class,getIssuesComplted,Method,DECLARES
ScoreCardSprintData,Class,setIssuesComplted,Method,DECLARES
setIssuesComplted,Method,Issuescomplted,Variable,USES
ScoreCardSprintData,Class,getIssuesRemoved,Method,DECLARES
ScoreCardSprintData,Class,setIssuesRemoved,Method,DECLARES
setIssuesRemoved,Method,Issuesremoved,Variable,USES
ScoreCardSprintData,Class,getIssuesRefined,Method,DECLARES
ScoreCardSprintData,Class,setIssuesRefined,Method,DECLARES
setIssuesRefined,Method,Issuesrefined,Variable,USES
ScoreCardSprintData,Class,setSprintName,Method,DECLARES
setSprintName,Method,Sprintname,Variable,USES
ScoreCardSprintData,Class,getSprintId,Method,DECLARES
ScoreCardSprintData,Class,setSprintId,Method,DECLARES
setSprintId,Method,Sprintid,Variable,USES
ScoreCardSprintData,Class,getRefinedDefects,Method,DECLARES
ScoreCardSprintData,Class,setRefinedDefects,Method,DECLARES
setRefinedDefects,Method,Refineddefects,Variable,USES
ScoreCardSprintData,Class,getState,Method,DECLARES
ScoreCardSprintData,Class,setState,Method,DECLARES
ScoreCardSprintData,Class,getSpillOverSp,Method,DECLARES
ScoreCardSprintData,Class,setSpillOverSp,Method,DECLARES
setSpillOverSp,Method,Spilloversp,Variable,USES
ScoreCardSprintData,Class,getIssueSpillover,Method,DECLARES
ScoreCardSprintData,Class,setIssueSpillover,Method,DECLARES
setIssueSpillover,Method,Issuespillover,Variable,USES
TransitionModel,Class,Wid,Variable,HAS_FIELD
TransitionModel,Class,Crstate,Variable,HAS_FIELD
TransitionModel,Class,Frmstate,Variable,HAS_FIELD
TransitionModel,Class,Mdfdate,Variable,HAS_FIELD
TransitionModel,Class,Waittime,Variable,HAS_FIELD
TransitionModel,Class,Prestatewaittime,Variable,HAS_FIELD
TransitionModel,Class,Createtime,Variable,HAS_FIELD
TransitionModel,Class,Effort,Variable,HAS_FIELD
TransitionModel,Class,Leadtime,Variable,HAS_FIELD
TransitionModel,Class,Pname,Variable,HAS_FIELD
TransitionModel,Class,Projkey,Variable,HAS_FIELD
TransitionModel,Class,Sname,Variable,HAS_FIELD
TransitionModel,Class,getwId,Method,DECLARES
TransitionModel,Class,setwId,Method,DECLARES
TransitionModel,Class,getPreStateWaitTime,Method,DECLARES
TransitionModel,Class,setPreStateWaitTime,Method,DECLARES
setPreStateWaitTime,Method,Prestatewaittime,Variable,USES
TransitionModel,Class,getCreateTime,Method,DECLARES
TransitionModel,Class,setCreateTime,Method,DECLARES
setCreateTime,Method,Createtime,Variable,USES
TransitionModel,Class,getEffort,Method,DECLARES
TransitionModel,Class,setEffort,Method,DECLARES
TransitionModel,Class,getLeadTime,Method,DECLARES
TransitionModel,Class,setLeadTime,Method,DECLARES
setLeadTime,Method,Leadtime,Variable,USES
TransitionModel,Class,getCrState,Method,DECLARES
TransitionModel,Class,setCrState,Method,DECLARES
setCrState,Method,Crstate,Variable,USES
TransitionModel,Class,getFrmState,Method,DECLARES
TransitionModel,Class,setFrmState,Method,DECLARES
setFrmState,Method,Frmstate,Variable,USES
TransitionModel,Class,getMdfDate,Method,DECLARES
TransitionModel,Class,setMdfDate,Method,DECLARES
setMdfDate,Method,Mdfdate,Variable,USES
TransitionModel,Class,getWaitTime,Method,DECLARES
TransitionModel,Class,setWaitTime,Method,DECLARES
setWaitTime,Method,Waittime,Variable,USES
TransitionModel,Class,getpName,Method,DECLARES
TransitionModel,Class,setpName,Method,DECLARES
TransitionModel,Class,getsName,Method,DECLARES
TransitionModel,Class,setsName,Method,DECLARES
TransitionModel,Class,getProjKey,Method,DECLARES
TransitionModel,Class,setProjKey,Method,DECLARES
VelocityList,Class,Chartdatacommitted,Variable,HAS_FIELD
VelocityList,Class,Chartdataafter,Variable,HAS_FIELD
VelocityList,Class,Chartdatacompleted,Variable,HAS_FIELD
VelocityList,Class,Chartdatafinalcommited,Variable,HAS_FIELD
VelocityList,Class,Chartdataremoved,Variable,HAS_FIELD
VelocityList,Class,Chartdataeffort,Variable,HAS_FIELD
VelocityList,Class,Chartdatadefects,Variable,HAS_FIELD
VelocityList,Class,Chartdatacapacity,Variable,HAS_FIELD
VelocityList,Class,Chartissuescomp,Variable,HAS_FIELD
VelocityList,Class,Chartissuescomm,Variable,HAS_FIELD
VelocityList,Class,Chartissuescommafter,Variable,HAS_FIELD
VelocityList,Class,Chartissuesrefined,Variable,HAS_FIELD
VelocityList,Class,Chartissuesremoved,Variable,HAS_FIELD
VelocityList,Class,Catagories,Variable,HAS_FIELD
VelocityList,Class,Finalcommitedstories,Variable,HAS_FIELD
VelocityList,Class,Finalcommiteddefetcs,Variable,HAS_FIELD
VelocityList,Class,Completedstories,Variable,HAS_FIELD
VelocityList,Class,Completeddefects,Variable,HAS_FIELD
VelocityList,Class,Startdate,Variable,HAS_FIELD
VelocityList,Class,Enddate,Variable,HAS_FIELD
VelocityList,Class,Workitemarr,Variable,HAS_FIELD
VelocityList,Class,Activesprint,Variable,HAS_FIELD
VelocityList,Class,getWorkItemArr,Method,DECLARES
VelocityList,Class,setWorkItemArr,Method,DECLARES
setWorkItemArr,Method,Workitemarr,Variable,USES
Almconfigrepo,Interface,findByProjectName,Method,DECLARES
Almconfigrepo,Interface,deleteByProjectName,Method,DECLARES
Changehisortyrepo,Interface,findByWId,Method,DECLARES
Changehisortyrepo,Interface,findByPName,Method,DECLARES
Configurationsettingrep,Interface,findByProjectName,Method,DECLARES
Configurationsettingrep,Interface,findByProjectNameIn,Method,DECLARES
Configurationsettingrep,Interface,deleteByProjectName,Method,DECLARES
Iterationrepo,Interface,findBySNameAndPName,Method,DECLARES
Iterationrepo,Interface,findBySNameAndPNameAndPAlmType,Method,DECLARES
Iterationrepo,Interface,findByPName,Method,DECLARES
Iterationrepo,Interface,findBySIdAndPName,Method,DECLARES
Metricrepo,Interface,findByPNameAndSName,Method,DECLARES
Metricrepo,Interface,findByPNameAndType,Method,DECLARES
Metricrepo,Interface,findByWIdAndPNameAndSName,Method,DECLARES
Metricrepo,Interface,findByWId,Method,DECLARES
Metricrepo,Interface,findByPNameAndSId,Method,DECLARES
Metricrepo,Interface,findByPNameAndTypeAndPAlmType,Method,DECLARES
Metricrepo,Interface,findByPNameAndSNameAndPAlmType,Method,DECLARES
Metricrepo,Interface,findByPNameAndSIdAndPAlmType,Method,DECLARES
Metricrepo,Interface,findByPNameAndSNameAndType,Method,DECLARES
Metricrepo,Interface,findByPName,Method,DECLARES
Metricrepo,Interface,findByPNameAndBaselineAndType,Method,DECLARES
Metricrepo,Interface,findByPNameAndWId,Method,DECLARES
Metricrepo,Interface,findByPNameAndPAlmType,Method,DECLARES
Metricrepo,Interface,findByWIdAndPName,Method,DECLARES
Transitionrepo,Interface,findByPNameAndSName,Method,DECLARES
Transitionrepo,Interface,findByWId,Method,DECLARES
Transitionrepo,Interface,findByPName,Method,DECLARES
ALMClientImplementation,Class,Logger,Variable,HAS_FIELD
ALMClientImplementation,Class,Almtype,Variable,HAS_FIELD
ALMClientImplementation,Class,Auth,Variable,HAS_FIELD
ALMClientImplementation,Class,Changehisortyrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Changelogs,Variable,HAS_FIELD
ALMClientImplementation,Class,Almconfigrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Almconfig,Variable,HAS_FIELD
ALMClientImplementation,Class,Portfolioviewconfig,Variable,HAS_FIELD
ALMClientImplementation,Class,Createddate,Variable,HAS_FIELD
ALMClientImplementation,Class,Creationtime,Variable,HAS_FIELD
ALMClientImplementation,Class,Ctx,Variable,HAS_FIELD
ALMClientImplementation,Class,Efforthistoryrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Effortinfo,Variable,HAS_FIELD
ALMClientImplementation,Class,Efforts,Variable,HAS_FIELD
ALMClientImplementation,Class,Fieldsofjsondata,Variable,HAS_FIELD
ALMClientImplementation,Class,Historyjsonoutput,Variable,HAS_FIELD
ALMClientImplementation,Class,Historyarray,Variable,HAS_FIELD
ALMClientImplementation,Class,Iteration,Variable,HAS_FIELD
ALMClientImplementation,Class,Iterationinfo,Variable,HAS_FIELD
ALMClientImplementation,Class,Iterationrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Iterationset,Variable,HAS_FIELD
ALMClientImplementation,Class,Metricrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Metrics,Variable,HAS_FIELD
ALMClientImplementation,Class,Metricsinfo,Variable,HAS_FIELD
ALMClientImplementation,Class,Metricsset,Variable,HAS_FIELD
ALMClientImplementation,Class,Origest,Variable,HAS_FIELD
ALMClientImplementation,Class,Projectname,Variable,HAS_FIELD
ALMClientImplementation,Class,Projectrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Portfolioviewconfigrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Sprintid,Variable,HAS_FIELD
ALMClientImplementation,Class,Sprintname,Variable,HAS_FIELD
ALMClientImplementation,Class,Multiplesprints,Variable,HAS_FIELD
ALMClientImplementation,Class,Sprintwisecalculation,Variable,HAS_FIELD
ALMClientImplementation,Class,Subtaskstatelist,Variable,HAS_FIELD
ALMClientImplementation,Class,Taskdetailslist,Variable,HAS_FIELD
ALMClientImplementation,Class,Taskeffort,Variable,HAS_FIELD
ALMClientImplementation,Class,Taskfirststate,Variable,HAS_FIELD
ALMClientImplementation,Class,Tasklaststate,Variable,HAS_FIELD
ALMClientImplementation,Class,Tasklist,Variable,HAS_FIELD
ALMClientImplementation,Class,Taskset,Variable,HAS_FIELD
ALMClientImplementation,Class,Transitioninfo,Variable,HAS_FIELD
ALMClientImplementation,Class,Transitionrepo,Variable,HAS_FIELD
ALMClientImplementation,Class,Wid,Variable,HAS_FIELD
ALMClientImplementation,Class,Rallyauth,Variable,HAS_FIELD
ALMClientImplementation,Class,Projkey,Variable,HAS_FIELD
ALMClientImplementation,Class,Rallyfeatureurl,Variable,HAS_FIELD
ALMClientImplementation,Class,Rallyfeatureend,Variable,HAS_FIELD
ALMClientImplementation,Class,Featureid,Variable,HAS_FIELD
ALMClientImplementation,Class,Template,Variable,HAS_FIELD
ALMClientImplementation,Class,Filter,Variable,HAS_FIELD
ALMClientImplementation,Class,getALMToolData,Method,DECLARES
ALMClientImplementation,Class,deleteIterations,Method,DECLARES
ALMClientImplementation,Class,sortIterationData,Method,DECLARES
ALMClientImplementation,Class,init,Method,DECLARES
ALMClientImplementation,Class,getSortedJson,Method,DECLARES
ALMClientImplementation,Class,populateMetrics,Method,DECLARES
ALMClientImplementation,Class,saveIteration,Method,DECLARES
ALMClientImplementation,Class,saveMetrics,Method,DECLARES
ALMClientImplementation,Class,setVariables,Method,DECLARES
ChartCalculations,Class,Transitionrepo,Variable,HAS_FIELD
ChartCalculations,Class,Almconfig,Variable,HAS_FIELD
customFieldNames,Class,Storypoints,Variable,HAS_FIELD
customFieldNames,Class,Targetrelease,Variable,HAS_FIELD
customFieldNames,Class,Component,Variable,HAS_FIELD
customFieldNames,Class,Fixversions,Variable,HAS_FIELD
customFieldNames,Class,Releasedate,Variable,HAS_FIELD
customFieldNames,Class,Modules,Variable,HAS_FIELD
customFieldNames,Class,Affectedversions,Variable,HAS_FIELD
customFieldNames,Class,Clientjiraid,Variable,HAS_FIELD
customFieldNames,Class,Defectseverity,Variable,HAS_FIELD
customFieldNames,Class,Baselinerequirement,Variable,HAS_FIELD
customFieldNames,Class,Jira_Sprint_Field_Brillio,Variable,HAS_FIELD
customFieldNames,Class,Jira_Field_Defect_Injector,Variable,HAS_FIELD
customFieldNames,Class,Target_Release,Variable,HAS_FIELD
customFieldNames,Class,Jira_Field_Defect_Type,Variable,HAS_FIELD
customFieldNames,Class,Kywrd_Status,Variable,HAS_FIELD
customFieldNames,Class,Taskstatelist,Variable,HAS_FIELD
customFieldNames,Class,Subtaskstatelist,Variable,HAS_FIELD
customFieldNames,Class,Storystatelist,Variable,HAS_FIELD
customFieldNames,Class,Bugstatelist,Variable,HAS_FIELD
customFieldNames,Class,Epicstatelist,Variable,HAS_FIELD
customFieldNames,Class,Teststatelist,Variable,HAS_FIELD
customFieldNames,Class,Newfeaturelist,Variable,HAS_FIELD
customFieldNames,Class,Improvementstatelist,Variable,HAS_FIELD
customFieldNames,Class,Bugexternalstatelist,Variable,HAS_FIELD
customFieldNames,Class,Newfeatureexternalstatelist,Variable,HAS_FIELD
customFieldNames,Class,Improvementexternalstatelist,Variable,HAS_FIELD
customFieldNames,Class,Taskexternalstatelist,Variable,HAS_FIELD
customFieldNames,Class,Subtaskexternalstatelist,Variable,HAS_FIELD
customFieldNames,Class,Defectsquads,Variable,HAS_FIELD
customFieldNames,Class,Defectcategory,Variable,HAS_FIELD
customFieldNames,Class,Whenfound,Variable,HAS_FIELD
customFieldNames,Class,Wherefound,Variable,HAS_FIELD
customFieldNames,Class,Howfound,Variable,HAS_FIELD
customFieldNames,Class,Environment,Variable,HAS_FIELD
customFieldNames,Class,Environmetkey,Variable,HAS_FIELD
customFieldNames,Class,Priority_Level,Variable,HAS_FIELD
customFieldNames,Class,Acfeature_Capability,Variable,HAS_FIELD
customFieldNames,Class,Url,Variable,HAS_FIELD
customFieldNames,Class,getCustomFieldsInfo,Method,DECLARES
customFieldNames,Class,getProjectStatus,Method,DECLARES
customFieldNames,Class,getBugStateList,Method,DECLARES
customFieldNames,Class,getStateList,Method,DECLARES
customFieldNames,Class,getEpicStateList,Method,DECLARES
customFieldNames,Class,getStoryStateList,Method,DECLARES
customFieldNames,Class,getSubTaskStateList,Method,DECLARES
customFieldNames,Class,getTestStateList,Method,DECLARES
customFieldNames,Class,getTaskStateList,Method,DECLARES
customFieldNames,Class,getCustomFieldsOfLpm,Method,DECLARES
deleteJiraIssues,Class,Logger,Variable,HAS_FIELD
deleteJiraIssues,Class,Auth,Variable,HAS_FIELD
deleteJiraIssues,Class,Ctx,Variable,HAS_FIELD
deleteJiraIssues,Class,Mongoagr,Variable,HAS_FIELD
deleteJiraIssues,Class,Widlist,Variable,HAS_FIELD
deleteJiraIssues,Class,Metrics,Variable,HAS_FIELD
deleteJiraIssues,Class,Metricsrepo,Variable,HAS_FIELD
deleteJiraIssues,Class,Wid,Variable,HAS_FIELD
deleteJiraIssues,Class,handleDeletedIssues,Method,DECLARES
handleDeletedIssues,Method,Ctx,Variable,USES
handleDeletedIssues,Method,Metricsrepo,Variable,USES
handleDeletedIssues,Method,Allmetrics,Variable,USES
handleDeletedIssues,Method,Groupbywid,Variable,USES
handleDeletedIssues,Method,Jsonoutput,Variable,USES
handleDeletedIssues,Method,Totalissues,Variable,USES
handleDeletedIssues,Method,Startat,Variable,USES
handleDeletedIssues,Method,Maxresults,Variable,USES
handleDeletedIssues,Method,Jsonarrayoutput,Variable,USES
handleDeletedIssues,Method,Metrics,Variable,USES
handleDeletedIssues,Method,Jsonnew,Variable,USES
handleDeletedIssues,Method,Wid,Variable,USES
handleDeletedIssues,Method,Tempmetrics,Variable,USES
handleDeletedIssues,Method,Recentmodel,Variable,USES
EffortAndChangeItemInfo,Class,Pname,Variable,HAS_FIELD
EffortAndChangeItemInfo,Class,Sname,Variable,HAS_FIELD
EffortAndChangeItemInfo,Class,populateSubArrays,Method,DECLARES
populateSubArrays,Method,Pname,Variable,USES
populateSubArrays,Method,Projectname,Variable,USES
populateSubArrays,Method,Sname,Variable,USES
populateSubArrays,Method,Sprintname,Variable,USES
populateSubArrays,Method,Loggedtime,Variable,USES
populateSubArrays,Method,Epiclink,Variable,USES
populateSubArrays,Method,Objectlist,Variable,USES
populateSubArrays,Method,Filteredstatusarray,Variable,USES
populateSubArrays,Method,Modifieddatelist,Variable,USES
populateSubArrays,Method,Efforts,Variable,USES
populateSubArrays,Method,Changelogs,Variable,USES
populateSubArrays,Method,Timeest,Variable,USES
populateSubArrays,Method,Timespent,Variable,USES
populateSubArrays,Method,Createddate,Variable,USES
populateSubArrays,Method,Author,Variable,USES
populateSubArrays,Method,Cruns,Variable,USES
populateSubArrays,Method,Effortmodel,Variable,USES
populateSubArrays,Method,Itemarray,Variable,USES
populateSubArrays,Method,Cm,Variable,USES
populateSubArrays,Method,Timeestimate,Variable,USES
populateSubArrays,Method,Timeremainingestimate,Variable,USES
populateSubArrays,Method,Effort,Variable,USES
IssueHierarchy,Class,getHierarchy,Method,DECLARES
IssueHierarchy,Class,getRelatedTaskInfo,Method,DECLARES
IssueHierarchy,Class,getUnMappedData,Method,DECLARES
IssueHierarchy,Class,getDataInStructure,Method,DECLARES
IssueHierarchy,Class,getHierarchyData,Method,DECLARES
IssueHierarchy,Class,getDataInTreeStructure,Method,DECLARES
IssueHierarchy,Class,getChildren,Method,DECLARES
IssueHierarchy,Class,getUnMappedInTreeStructure,Method,DECLARES
IssueHierarchy,Class,Allmetrics,Variable,HAS_FIELD
IssueHierarchy,Class,Mappeddata,Variable,HAS_FIELD
IssueHierarchy,Class,Unmapped,Variable,HAS_FIELD
getHierarchy,Method,Tempcopy,Variable,USES
getHierarchy,Method,Tempissue,Variable,USES
getHierarchy,Method,Epiclinks,Variable,USES
getHierarchy,Method,Epics,Variable,USES
getHierarchy,Method,Story,Variable,USES
getHierarchy,Method,Unmapped,Variable,USES
getHierarchy,Method,Child,Variable,USES
getHierarchy,Method,Storydata,Variable,USES
getHierarchy,Method,Group,Variable,USES
getRelatedTaskInfo,Method,Tempcopy,Variable,USES
getRelatedTaskInfo,Method,Tempissue,Variable,USES
getUnMappedData,Method,Unmapped,Variable,USES
getDataInStructure,Method,Relateddata,Variable,USES
getDataInStructure,Method,Subtasks,Variable,USES
getDataInStructure,Method,Epiclinks,Variable,USES
getDataInStructure,Method,Childdata,Variable,USES
getDataInStructure,Method,Tempmetrics,Variable,USES
getHierarchyData,Method,Allmetrics,Variable,USES
getHierarchyData,Method,Hierarchyview,Variable,USES
getHierarchyData,Method,Unmappedhierarchyview,Variable,USES
getHierarchyData,Method,Hierarchymap,Variable,USES
getDataInTreeStructure,Method,Hierarchyview,Variable,USES
getDataInTreeStructure,Method,Subtaklist,Variable,USES
getDataInTreeStructure,Method,Epiclinks,Variable,USES
getDataInTreeStructure,Method,Node,Variable,USES
getDataInTreeStructure,Method,Storyid,Variable,USES
getDataInTreeStructure,Method,Linksdata,Variable,USES
getDataInTreeStructure,Method,Children,Variable,USES
getChildren,Method,Children,Variable,USES
getChildren,Method,Links,Variable,USES
getChildren,Method,Child,Variable,USES
getChildren,Method,Tempissuelist,Variable,USES
getChildren,Method,Tempissue,Variable,USES
getUnMappedInTreeStructure,Method,Nodelist,Variable,USES
getUnMappedInTreeStructure,Method,Node,Variable,USES
getUnMappedInTreeStructure,Method,Children,Variable,USES
getUnMappedInTreeStructure,Method,Child,Variable,USES
IterationInfo,Class,Sprintid,Variable,HAS_FIELD
IterationInfo,Class,Iteration,Variable,HAS_FIELD
IterationInfo,Class,Sprintname,Variable,HAS_FIELD
IterationInfo,Class,Iterationset,Variable,HAS_FIELD
IterationInfo,Class,Logger,Variable,HAS_FIELD
IterationInfo,Class,populateIteration,Method,DECLARES
populateIteration,Method,Response,Variable,USES
populateIteration,Method,Sprintset,Variable,USES
populateIteration,Method,Objectlist,Variable,USES
populateIteration,Method,Iteration,Variable,USES
populateIteration,Method,Tempkey,Variable,USES
populateIteration,Method,Sprintjson,Variable,USES
populateIteration,Method,Sprintstatus,Variable,USES
populateIteration,Method,Sprintname,Variable,USES
populateIteration,Method,Startdate,Variable,USES
populateIteration,Method,Enddate,Variable,USES
populateIteration,Method,Completeddate,Variable,USES
populateIteration,Method,Id,Variable,USES
populateIteration,Method,Multiplesprints,Variable,USES
populateIteration,Method,Sprintdata,Variable,USES
populateIteration,Method,Sprintid,Variable,USES
populateIteration,Method,Modeliterator,Variable,USES
IterationInfo,Class,getSprintInfo,Method,DECLARES
getSprintInfo,Method,Separationstring1,Variable,USES
JIRAApplication,Class,Logger,Variable,HAS_FIELD
JIRAApplication,Class,Ctx,Variable,HAS_FIELD
JIRAApplication,Class,Almclientmetrics,Variable,HAS_FIELD
JIRAApplication,Class,Configurationrepo,Variable,HAS_FIELD
JIRAApplication,Class,Configurationcolection,Variable,HAS_FIELD
JIRAApplication,Class,Metric,Variable,HAS_FIELD
JIRAApplication,Class,Metric1,Variable,HAS_FIELD
JIRAApplication,Class,jiraMain,Method,DECLARES
JIRAApplication,Class,cleanObject,Method,DECLARES
JIRAApplication,Class,deleteJiraIssues,Method,DECLARES
jiraMain,Method,Ctx,Variable,USES
jiraMain,Method,Almclientmetrics,Variable,USES
jiraMain,Method,Configurationrepo,Variable,USES
jiraMain,Method,Configurationcolection,Variable,USES
jiraMain,Method,Metric,Variable,USES
jiraMain,Method,Metric1,Variable,USES
cleanObject,Method,Almclientmetrics,Variable,USES
cleanObject,Method,Configurationrepo,Variable,USES
cleanObject,Method,Configurationcolection,Variable,USES
cleanObject,Method,Metric,Variable,USES
cleanObject,Method,Metric1,Variable,USES
deleteJiraIssues,Method,Ctx,Variable,USES
deleteJiraIssues,Method,Configurationrepo,Variable,USES
deleteJiraIssues,Method,Configurationcolection,Variable,USES
deleteJiraIssues,Method,Metric,Variable,USES
deleteJiraIssues,Method,Metric1,Variable,USES
JiraAuthentication,Class,Utf,Variable,HAS_FIELD
JiraAuthentication,Class,Logger,Variable,HAS_FIELD
JiraAuthentication,Class,jiraConnectionForStatus,Method,DECLARES
jiraConnectionForStatus,Method,Connectionstatus,Variable,USES
jiraConnectionForStatus,Method,Jiraurl,Variable,USES
jiraConnectionForStatus,Method,Loginsearch,Variable,USES
jiraConnectionForStatus,Method,Encodedbytes,Variable,USES
jiraConnectionForStatus,Method,Logincreds,Variable,USES
jiraConnectionForStatus,Method,Basicauth,Variable,USES
jiraConnectionForStatus,Method,In,Variable,USES
jiraConnection,Method,Connection,Variable,USES
jiraConnection,Method,Datatj,Variable,USES
jiraConnection,Method,Jiraurl,Variable,USES
JiraAuthentication,Class,jiraConnection,Method,DECLARES
JiraAuthentication,Class,getBoardFilterId,Method,DECLARES
getBoardFilterId,Method,Jiraurl,Variable,USES
getBoardFilterId,Method,Url,Variable,USES
getBoardFilterId,Method,Filterid,Variable,USES
getBoardFilterId,Method,Connection,Variable,USES
getBoardFilterId,Method,Ur,Variable,USES
getBoardFilterId,Method,Login1,Variable,USES
getBoardFilterId,Method,Encodedbytes,Variable,USES
getBoardFilterId,Method,Logincreds,Variable,USES
getBoardFilterId,Method,Basicauth,Variable,USES
getBoardFilterId,Method,In,Variable,USES
getBoardFilterId,Method,Teststringnew,Variable,USES
getBoardFilterId,Method,Parser,Variable,USES
JiraAuthentication,Class,alternateJiraConnection,Method,DECLARES
alternateJiraConnection,Method,Connection,Variable,USES
alternateJiraConnection,Method,Jiraurl,Variable,USES
alternateJiraConnection,Method,Ur,Variable,USES
alternateJiraConnection,Method,Login1,Variable,USES
alternateJiraConnection,Method,Encodedbytes,Variable,USES
alternateJiraConnection,Method,Logincreds,Variable,USES
alternateJiraConnection,Method,Basicauth,Variable,USES
alternateJiraConnection,Method,In,Variable,USES
alternateJiraConnection,Method,Teststringnew,Variable,USES
JiraAuthentication,Class,get,Method,DECLARES
get,Method,Requestfactory,Variable,USES
JiraAuthentication,Class,makeRestCall,Method,DECLARES
makeRestCall,Method,Builder,Variable,USES
makeRestCall,Method,Uricomponents,Variable,USES
makeRestCall,Method,Uri,Variable,USES
JiraAuthentication,Class,createHeaders,Method,DECLARES
createHeaders,Method,Auth,Variable,USES
createHeaders,Method,Encodedauth,Variable,USES
createHeaders,Method,Authheader,Variable,USES
createHeaders,Method,Headers,Variable,USES
Jiraclient,Interface,getALMToolData,Method,DECLARES
JiraExceptions,Class,Serialversionuid,Variable,HAS_FIELD
MetricsInfo,Class,getAssignee,Method,DECLARES
MetricsInfo,Class,getDescription,Method,DECLARES
MetricsInfo,Class,getFixVersion,Method,DECLARES
getFixVersion,Method,Fixversionmap,Variable,USES
getFixVersion,Method,Reldate,Variable,USES
getFixVersion,Method,Time,Variable,USES
getFixVersion,Method,Relname,Variable,USES
MetricsInfo,Class,getIssueLinks,Method,DECLARES
getIssueLinks,Method,Issuelinkjson,Variable,USES
getIssueLinks,Method,Outwardlist,Variable,USES
getIssueLinks,Method,Jsonlinkfield,Variable,USES
getIssueLinks,Method,Outwardissue,Variable,USES
getIssueLinks,Method,Inwardissue,Variable,USES
MetricsInfo,Class,getPriority,Method,DECLARES
getPriority,Method,Tempkey,Variable,USES
MetricsInfo,Class,getSprintAllocation,Method,DECLARES
getSprintAllocation,Method,Sprintalocationdate,Variable,USES
getSprintAllocation,Method,Historyobject,Variable,USES
getSprintAllocation,Method,Itemsarray,Variable,USES
RallyAuthentication,Class,Utf,Variable,HAS_FIELD
RallyAuthentication,Class,Logger,Variable,HAS_FIELD
RallyAuthentication,Class,createHeaders,Method,DECLARES
RallyAuthentication,Class,createAPIHeaders,Method,DECLARES
createAPIHeaders,Method,Headers,Variable,USES
RallyAuthentication,Class,makeRestCallAPI,Method,DECLARES
RallyAuthentication,Class,get,Method,DECLARES
RallyAuthentication,Class,callRallyUrl,Method,DECLARES
callRallyUrl,Method,Rallyjson,Variable,USES
callRallyUrl,Method,Arrresult,Variable,USES
callRallyUrl,Method,Response,Variable,USES
callRallyUrl,Method,Feature,Variable,USES
ReleaseInfo,Class,Releasecount,Variable,HAS_FIELD
ReleaseInfo,Class,Donecount,Variable,HAS_FIELD
ReleaseInfo,Class,Almconfigrepo,Variable,HAS_FIELD
ReleaseInfo,Class,Almconfiguration,Variable,HAS_FIELD
ReleaseInfo,Class,Repo,Variable,HAS_FIELD
ReleaseInfo,Class,getReleaseDetails,Method,DECLARES
getReleaseDetails,Method,Repo,Variable,USES
getReleaseDetails,Method,Almconfigrepo,Variable,USES
getReleaseDetails,Method,Almconfiguration,Variable,USES
getReleaseDetails,Method,Releasedetails,Variable,USES
getReleaseDetails,Method,Releases,Variable,USES
getReleaseDetails,Method,Releaselist,Variable,USES
getReleaseDetails,Method,Release,Variable,USES
getReleaseDetails,Method,Releasejson,Variable,USES
getReleaseDetails,Method,It,Variable,USES
getReleaseDetails,Method,En,Variable,USES
getReleaseDetails,Method,It1,Variable,USES
ReleaseInfo,Class,cleanObject,Method,DECLARES
cleanObject,Method,Almconfigrepo,Variable,USES
cleanObject,Method,Almconfiguration,Variable,USES
cleanObject,Method,Repo,Variable,USES
ReleaseInfo,Class,getTime,Method,DECLARES
getTime,Method,Parsedate,Variable,USES
getTime,Method,Milliseconds,Variable,USES
ReleaseInfo,Class,getReleaseStoryCount,Method,DECLARES
getReleaseStoryCount,Method,Mrepo,Variable,USES
getReleaseStoryCount,Method,Metricslist,Variable,USES
getReleaseStoryCount,Method,Storycount,Variable,USES
getReleaseStoryCount,Method,Donestorycount,Variable,USES
getReleaseStoryCount,Method,Listit,Variable,USES
getReleaseStoryCount,Method,Metricsmodel,Variable,USES
getReleaseStoryCount,Method,Entry,Variable,USES
getReleaseStoryCount,Method,Xit,Variable,USES
getReleaseStoryCount,Method,Xmap,Variable,USES
SprintWiseCalculation,Class,Logger,Variable,HAS_FIELD
SprintWiseCalculation,Class,Iterationrepo,Variable,HAS_FIELD
SprintWiseCalculation,Class,Ctx,Variable,HAS_FIELD
SprintWiseCalculation,Class,Metricsrepo,Variable,HAS_FIELD
SprintWiseCalculation,Class,Agg,Variable,HAS_FIELD
SprintWiseCalculation,Class,Model,Variable,HAS_FIELD
SprintWiseCalculation,Class,Assignedto,Variable,HAS_FIELD
SprintWiseCalculation,Class,Sprintmap,Variable,HAS_FIELD
SprintWiseCalculation,Class,Startdate,Variable,HAS_FIELD
SprintWiseCalculation,Class,Enddate,Variable,HAS_FIELD
SprintWiseCalculation,Class,Sprintname,Variable,HAS_FIELD
SprintWiseCalculation,Class,Projectname,Variable,HAS_FIELD
SprintWiseCalculation,Class,Closedstate,Variable,HAS_FIELD
SprintWiseCalculation,Class,Alm_Story,Variable,HAS_FIELD
SprintWiseCalculation,Class,Configuration,Variable,HAS_FIELD
SprintWiseCalculation,Class,Lastsprintenddate,Variable,HAS_FIELD
SprintWiseCalculation,Class,getSprintData,Method,DECLARES
SprintWiseCalculation,Class,getTeamsize,Method,DECLARES
SprintWiseCalculation,Class,getPlannedStoryPoint,Method,DECLARES
SprintWiseCalculation,Class,getSCMDetails,Method,DECLARES
SprintWiseCalculation,Class,getBuildFailure,Method,DECLARES
SprintWiseCalculation,Class,getTD,Method,DECLARES
SprintWiseCalculation,Class,getCrtItr,Method,DECLARES
SprintWiseCalculation,Class,getDefectsSev,Method,DECLARES
SprintWiseCalculation,Class,storyEffortCalculation,Method,DECLARES
SprintWiseCalculation,Class,getTotalEffort,Method,DECLARES
TransitionInfo,Class,populateTransition,Method,DECLARES
populateTransition,Method,Filteredstatusarray,Variable,USES
populateTransition,Method,Modifieddatelist,Variable,USES
populateTransition,Method,Tasklaststate,Variable,USES
populateTransition,Method,Taskeffort,Variable,USES
populateTransition,Method,Creationtime,Variable,USES
populateTransition,Method,Taskdetailslist,Variable,USES
populateTransition,Method,Modifieddate,Variable,USES
populateTransition,Method,Statewaittime,Variable,USES
populateTransition,Method,Previousstatewaittime,Variable,USES
populateTransition,Method,Leadtime,Variable,USES
populateTransition,Method,Taskdetails,Variable,USES
populateTransition,Method,Fromstate,Variable,USES
populateTransition,Method,Tostate,Variable,USES
populateTransition,Method,Filteredstatusjsonobject,Variable,USES
TransitionMetrices,Class,Filteredstatusarray,Variable,HAS_FIELD
TransitionMetrices,Class,Modifieddatelist,Variable,HAS_FIELD
TransitionMetrices,Class,Wid,Variable,HAS_FIELD
TransitionMetrices,Class,Laststate,Variable,HAS_FIELD
TransitionMetrices,Class,Effort,Variable,HAS_FIELD
TransitionMetrices,Class,Crtime,Variable,HAS_FIELD
TransitionMetrices,Class,Firststate,Variable,HAS_FIELD
TransitionMetrices,Class,Taskdetailslist,Variable,HAS_FIELD
TransitionMetrices,Class,Pname,Variable,HAS_FIELD
TransitionMetrices,Class,Sname,Variable,HAS_FIELD
TransitionMetrices,Class,Projkey,Variable,HAS_FIELD
TransitionMetrices,Class,getProjKey,Method,DECLARES
TransitionMetrices,Class,setProjKey,Method,DECLARES
TransitionMetrices,Class,getFilteredStatusArray,Method,DECLARES
TransitionMetrices,Class,setFilteredStatusArray,Method,DECLARES
setFilteredStatusArray,Method,Filteredstatusarray,Variable,USES
TransitionMetrices,Class,getModifiedDateList,Method,DECLARES
TransitionMetrices,Class,setModifiedDateList,Method,DECLARES
setModifiedDateList,Method,Modifieddatelist,Variable,USES
TransitionMetrices,Class,getwId,Method,DECLARES
TransitionMetrices,Class,setwId,Method,DECLARES
TransitionMetrices,Class,getLastState,Method,DECLARES
TransitionMetrices,Class,setLastState,Method,DECLARES
setLastState,Method,Laststate,Variable,USES
TransitionMetrices,Class,getEffort,Method,DECLARES
TransitionMetrices,Class,setEffort,Method,DECLARES
TransitionMetrices,Class,getCrTime,Method,DECLARES
TransitionMetrices,Class,setCrTime,Method,DECLARES
setCrTime,Method,Crtime,Variable,USES
TransitionMetrices,Class,getFirstState,Method,DECLARES
TransitionMetrices,Class,setFirstState,Method,DECLARES
setFirstState,Method,Firststate,Variable,USES
TransitionMetrices,Class,getTaskDetailsList,Method,DECLARES
TransitionMetrices,Class,setTaskDetailsList,Method,DECLARES
setTaskDetailsList,Method,Taskdetailslist,Variable,USES
TransitionMetrices,Class,getpName,Method,DECLARES
TransitionMetrices,Class,setpName,Method,DECLARES
TransitionMetrices,Class,getsName,Method,DECLARES
TransitionMetrices,Class,setsName,Method,DECLARES
BacklogCalculation,Class,Ctx,Variable,HAS_FIELD
BacklogCalculation,Class,Commonfunc,Variable,HAS_FIELD
BacklogCalculation,Class,Metricrepo,Variable,HAS_FIELD
BacklogCalculation,Class,Projectiterationrepo,Variable,HAS_FIELD
BacklogCalculation,Class,Logger,Variable,HAS_FIELD
BacklogCalculation,Class,Almconfiguration,Variable,HAS_FIELD
BacklogCalculation,Class,Allbugs,Variable,HAS_FIELD
BacklogCalculation,Class,Alliterations,Variable,HAS_FIELD
BacklogCalculation,Class,Alliterationsandbacklog,Variable,HAS_FIELD
BacklogCalculation,Class,Closestates,Variable,HAS_FIELD
BacklogCalculation,Class,getInititialDetails,Method,DECLARES
getInititialDetails,Method,Almconfigrepo,Variable,USES
getInititialDetails,Method,Almconfiguration,Variable,USES
getInititialDetails,Method,Projectiterationrepo,Variable,USES
getInititialDetails,Method,Mongo,Variable,USES
getInititialDetails,Method,Alliterations,Variable,USES
getInititialDetails,Method,Futureconst,Variable,USES
getInititialDetails,Method,Futurelist,Variable,USES
getInititialDetails,Method,Alliterationsandbacklog,Variable,USES
BacklogCalculation,Class,caluclateStoryAgeing2,Method,DECLARES
caluclateStoryAgeing2,Method,Response,Variable,USES
caluclateStoryAgeing2,Method,Closestates,Variable,USES
caluclateStoryAgeing2,Method,Currenttime,Variable,USES
caluclateStoryAgeing2,Method,Componentlists,Variable,USES
caluclateStoryAgeing2,Method,Storyinsightlist,Variable,USES
caluclateStoryAgeing2,Method,Stories,Variable,USES
caluclateStoryAgeing2,Method,Metricagedata,Variable,USES
caluclateStoryAgeing2,Method,Age,Variable,USES
caluclateStoryAgeing2,Method,Status,Variable,USES
caluclateStoryAgeing2,Method,Time,Variable,USES
caluclateStoryAgeing2,Method,Transitions,Variable,USES
caluclateStoryAgeing2,Method,Storyworkflowlist,Variable,USES
caluclateStoryAgeing2,Method,Trans,Variable,USES
caluclateStoryAgeing2,Method,Sp,Variable,USES
caluclateStoryAgeing2,Method,Sps,Variable,USES
caluclateStoryAgeing2,Method,Keyssp,Variable,USES
BacklogCalculation,Class,pushStateFlowObject2,Method,DECLARES
pushStateFlowObject2,Method,Starttime,Variable,USES
pushStateFlowObject2,Method,Waittime,Variable,USES
pushStateFlowObject2,Method,Endtime,Variable,USES
pushStateFlowObject2,Method,Allocations,Variable,USES
pushStateFlowObject2,Method,Allocdates,Variable,USES
pushStateFlowObject2,Method,Sprintids,Variable,USES
pushStateFlowObject2,Method,Ind,Variable,USES
pushStateFlowObject2,Method,Allocdate,Variable,USES
pushStateFlowObject2,Method,Filtersprints,Variable,USES
pushStateFlowObject2,Method,Sprintstartdate,Variable,USES
BacklogCalculation,Class,calculateGroomingTable,Method,DECLARES
calculateGroomingTable,Method,Responsedata,Variable,USES
calculateGroomingTable,Method,Metircs,Variable,USES
calculateGroomingTable,Method,Closedstates,Variable,USES
calculateGroomingTable,Method,Componentlists,Variable,USES
calculateGroomingTable,Method,Filtermetrics,Variable,USES
calculateGroomingTable,Method,Itr,Variable,USES
calculateGroomingTable,Method,Totalstories,Variable,USES
calculateGroomingTable,Method,Totalstorypoints,Variable,USES
calculateGroomingTable,Method,Completedstories,Variable,USES
calculateGroomingTable,Method,Completedstorypoints,Variable,USES
calculateGroomingTable,Method,Pendingestimatedstories,Variable,USES
calculateGroomingTable,Method,Pendingestimatedstorypoints,Variable,USES
calculateGroomingTable,Method,Pendingnonestimatedstories,Variable,USES
calculateGroomingTable,Method,Pendingnonestimatedstorypoints,Variable,USES
calculateGroomingTable,Method,Lastvalue,Variable,USES
BacklogCalculation,Class,Calculatestoryageing,Method,DECLARES
BuildCalculations,Class,Ctx,Variable,HAS_FIELD
BuildCalculations,Class,Buildrepo,Variable,HAS_FIELD
BuildCalculations,Class,Builddata,Variable,HAS_FIELD
BuildCalculations,Class,Builddatalist,Variable,HAS_FIELD
BuildCalculations,Class,Lastbuild,Variable,HAS_FIELD
BuildCalculations,Class,Jobdetails,Variable,HAS_FIELD
BuildCalculations,Class,Jobdetailsrepo,Variable,HAS_FIELD
BuildCalculations,Class,Mongo,Variable,HAS_FIELD
BuildCalculations,Class,Logger,Variable,HAS_FIELD
BuildCalculations,Class,getValueStreamSteps,Method,DECLARES
BuildCalculations,Class,buildStepArray,Method,DECLARES
BuildCalculations,Class,getFormattedDate,Method,DECLARES
BuildCalculations,Class,getState,Method,DECLARES
BuildCalculations,Class,getValueStreamPipelineJobs,Method,DECLARES
BuildCalculations,Class,calculateChildSteps,Method,DECLARES
BuildCalculations,Class,getMetric,Method,DECLARES
BuildCalculations,Class,preparePipeLine,Method,DECLARES
BuildCalculations,Class,prepareJobList,Method,DECLARES
BuildCalculations,Class,getGitlabValueStreamSteps,Method,DECLARES
CommonFunctions,Class,getComponentList,Method,DECLARES
getComponentList,Method,Componentlist,Variable,USES
getComponentList,Method,Component,Variable,USES
CommonFunctions,Class,convertToDisplayValues,Method,DECLARES
convertToDisplayValues,Method,Seconds,Variable,USES
convertToDisplayValues,Method,Minutes,Variable,USES
convertToDisplayValues,Method,Hours,Variable,USES
convertToDisplayValues,Method,Leftminutes,Variable,USES
convertToDisplayValues,Method,Days,Variable,USES
convertToDisplayValues,Method,Lefthours,Variable,USES
convertToDisplayValues,Method,Week,Variable,USES
convertToDisplayValues,Method,Leftdays,Variable,USES
CommonFunctions,Class,convertMilisToDisplayValuesDefect,Method,DECLARES
convertMilisToDisplayValuesDefect,Method,Seconds,Variable,USES
convertMilisToDisplayValuesDefect,Method,Minutes,Variable,USES
convertMilisToDisplayValuesDefect,Method,Hours,Variable,USES
convertMilisToDisplayValuesDefect,Method,Leftminutes,Variable,USES
convertMilisToDisplayValuesDefect,Method,Days,Variable,USES
convertMilisToDisplayValuesDefect,Method,Lefthours,Variable,USES
CommonFunctions,Class,convertSecondsToStringDisplay,Method,DECLARES
convertSecondsToStringDisplay,Method,Minutes,Variable,USES
convertSecondsToStringDisplay,Method,Hours,Variable,USES
convertSecondsToStringDisplay,Method,Leftminutes,Variable,USES
convertSecondsToStringDisplay,Method,Days,Variable,USES
convertSecondsToStringDisplay,Method,Lefthours,Variable,USES
CommonFunctions,Class,toHoursString,Method,DECLARES
toHoursString,Method,Minutes,Variable,USES
toHoursString,Method,Hours,Variable,USES
CommonFunctions,Class,toDaysString,Method,DECLARES
toDaysString,Method,Seconds,Variable,USES
toDaysString,Method,Minutes,Variable,USES
toDaysString,Method,Hours,Variable,USES
toDaysString,Method,Days,Variable,USES
Constant,Class,Prodtypeprior,Variable,HAS_FIELD
Constant,Class,Prodtypesev,Variable,HAS_FIELD
Constant,Class,Prodtypenewdef,Variable,HAS_FIELD
Constant,Class,Prodtypewaitdef,Variable,HAS_FIELD
Constant,Class,Prodsev,Variable,HAS_FIELD
CryptoUtils,Class,getRandomNonce,Method,DECLARES
getRandomNonce,Method,Nonce,Variable,USES
CryptoUtils,Class,getAESKey,Method,DECLARES
getAESKey,Method,Keygen,Variable,USES
CryptoUtils,Class,getAESKeyFromPassword,Method,DECLARES
getAESKeyFromPassword,Method,Factory,Variable,USES
getAESKeyFromPassword,Method,Spec,Variable,USES
CryptoUtils,Class,hex,Method,DECLARES
CryptoUtils,Class,hexWithBlockSize,Method,DECLARES
hexWithBlockSize,Method,Hex,Variable,USES
hexWithBlockSize,Method,Blocksize,Variable,USES
DefectCalculations,Class,Ctx,Variable,HAS_FIELD
DefectCalculations,Class,Commonfunc,Variable,HAS_FIELD
DefectCalculations,Class,Metricrepo,Variable,HAS_FIELD
DefectCalculations,Class,Logger,Variable,HAS_FIELD
DefectCalculations,Class,Almconfiguration,Variable,HAS_FIELD
DefectCalculations,Class,Allbugs,Variable,HAS_FIELD
DefectCalculations,Class,Alliterations,Variable,HAS_FIELD
DefectCalculations,Class,Alliterationsandbacklog,Variable,HAS_FIELD
DefectCalculations,Class,Closestates,Variable,HAS_FIELD
DefectCalculations,Class,getInititialDetails,Method,DECLARES
DefectCalculations,Class,calculateDefectInsightDataComponent,Method,DECLARES
DefectCalculations,Class,calculateDefectInsightData,Method,DECLARES
DefectCalculations,Class,meanTimeCalculation,Method,DECLARES
DefectCalculations,Class,Meantimecalcflag,Variable,HAS_FIELD
DefectCalculations,Class,Almconfigrepo,Variable,HAS_FIELD
DefectCalculations,Class,Totalrem,Variable,HAS_FIELD
DefectCalculations,Class,Totalspent,Variable,HAS_FIELD
DefectCalculations,Class,Totmetrics,Variable,HAS_FIELD
DefectCalculations,Class,Filtermetricsdata,Variable,HAS_FIELD
DefectCalculations,Class,Filterstroydata,Variable,HAS_FIELD
DefectCalculations,Class,Currentdate,Variable,HAS_FIELD
DefectCalculations,Class,Firstsprintstartdate,Variable,HAS_FIELD
DefectCalculations,Class,Mongo,Variable,HAS_FIELD
DefectCalculations,Class,Widarr,Variable,HAS_FIELD
DefectCalculations,Class,Issuelist,Variable,HAS_FIELD
DefectCalculations,Class,Workingbacklog,Variable,HAS_FIELD
DefectCalculations,Class,Workingsprints,Variable,HAS_FIELD
DefectCalculations,Class,Workitemarr2,Variable,HAS_FIELD
DefectCalculations,Class,Refinedissulist,Variable,HAS_FIELD
DefectCalculations,Class,Sprintanddefectsadded,Variable,HAS_FIELD
DefectCalculations,Class,Sprintanddefectsclosed,Variable,HAS_FIELD
DefectCalculations,Class,pushStateFlowObject,Method,DECLARES
DefectCalculations,Class,componentSprintDefectTrend,Method,DECLARES
DefectCalculations,Class,sprintDefectTrend,Method,DECLARES
DefectCalculations,Class,defectClassification,Method,DECLARES
DefectCalculations,Class,getParetoDataComp,Method,DECLARES
DefectCalculations,Class,getParetoData,Method,DECLARES
ValueComparator,Class,compare,Method,DECLARES
DefectCalculations,Class,getMetricsData,Method,DECLARES
DefectCalculations,Class,groupDataForPareto,Method,DECLARES
DefectCalculations,Class,getDefectProducationSlippageComp,Method,DECLARES
DefectCalculations,Class,getDefectProducationSlippage,Method,DECLARES
DefectCalculations,Class,getDefectDensityComp,Method,DECLARES
DefectCalculations,Class,getDefectDensity,Method,DECLARES
DefectCalculations,Class,getMerics,Method,DECLARES
DefectCalculations,Class,getStoryPoint,Method,DECLARES
DefectCalculations,Class,getDefectBacklogComponent,Method,DECLARES
DefectCalculations,Class,getDefectBacklog,Method,DECLARES
DefectCalculations,Class,callSP,Method,DECLARES
DefectCalculations,Class,calcClosedDefects,Method,DECLARES
DefectCalculations,Class,calcVelocity,Method,DECLARES
DefectCalculations,Class,refinedIssues,Method,DECLARES
DefectCalculations,Class,checkRemoved,Method,DECLARES
DefectCalculations,Class,checkWithdrawn,Method,DECLARES
DefectCalculations,Class,filterTrans,Method,DECLARES
getInititialDetails,Method,Metricrepo,Variable,USES
getInititialDetails,Method,Closestates,Variable,USES
DefectCalculations,Class,Sum,Variable,HAS_FIELD
groupDataForPareto,Method,Groupeddatabymodule,Variable,USES
getDefectProducationSlippageComp,Method,Componentwisedata,Variable,USES
getMetricsData,Method,Metricssource,Variable,USES
ValueComparator,Class,Base,Variable,HAS_FIELD
EncryptionDecryptionAES,Class,Logger,Variable,HAS_FIELD
EncryptionDecryptionAES,Class,Utf_8,Variable,HAS_FIELD
EncryptionDecryptionAES,Class,encrypt,Method,DECLARES
encrypt,Method,Ptext,Variable,USES
EncryptionDecryptionAES,Class,decrypt,Method,DECLARES
EncryptorAesGcmPassword,Class,Encrypt_Algo,Variable,HAS_FIELD
EncryptorAesGcmPassword,Class,Tag_Length_Bit,Variable,HAS_FIELD
EncryptorAesGcmPassword,Class,Iv_Length_Byte,Variable,HAS_FIELD
EncryptorAesGcmPassword,Class,Salt_Length_Byte,Variable,HAS_FIELD
EncryptorAesGcmPassword,Class,Utf_8,Variable,HAS_FIELD
EncryptorAesGcmPassword,Class,encrypt,Method,DECLARES
encrypt,Method,Salt,Variable,USES
encrypt,Method,Iv,Variable,USES
encrypt,Method,Aeskeyfrompassword,Variable,USES
encrypt,Method,Cipher,Variable,USES
encrypt,Method,Ciphertext,Variable,USES
encrypt,Method,Ciphertextwithivsalt,Variable,USES
EncryptorAesGcmPassword,Class,decrypt,Method,DECLARES
decrypt,Method,Decode,Variable,USES
decrypt,Method,Bb,Variable,USES
decrypt,Method,Iv,Variable,USES
decrypt,Method,Salt,Variable,USES
decrypt,Method,Ciphertext,Variable,USES
decrypt,Method,Aeskeyfrompassword,Variable,USES
decrypt,Method,Cipher,Variable,USES
decrypt,Method,Plaintext,Variable,USES
RestClient,Class,Logger,Variable,HAS_FIELD
RestClient,Class,get,Method,DECLARES
RestClient,Class,makeGetRestCall,Method,DECLARES
makeGetRestCall,Method,Builder,Variable,USES
makeGetRestCall,Method,Uricomponents,Variable,USES
makeGetRestCall,Method,Uri,Variable,USES
RestClient,Class,createBasicAuthHeaders,Method,DECLARES
createBasicAuthHeaders,Method,Auth,Variable,USES
createBasicAuthHeaders,Method,Encodedauth,Variable,USES
createBasicAuthHeaders,Method,Authheader,Variable,USES
createBasicAuthHeaders,Method,Headers,Variable,USES
RestClient,Class,cleanUp,Method,DECLARES
cleanUp,Method,Coverage,Variable,USES
cleanUp,Method,Zip,Variable,USES
RestClient,Class,downloadXML,Method,DECLARES
downloadXML,Method,Authstring,Variable,USES
downloadXML,Method,Authencbytes,Variable,USES
downloadXML,Method,Authstringenc,Variable,USES
downloadXML,Method,Url,Variable,USES
downloadXML,Method,Urlconnection,Variable,USES
downloadXML,Method,Authheader,Variable,USES
downloadXML,Method,Is,Variable,USES
downloadXML,Method,Source,Variable,USES
downloadXML,Method,Target,Variable,USES
RestClient,Class,deleteDirectory,Method,DECLARES
RestClient,Class,unZipIt,Method,DECLARES
unZipIt,Method,Buffer,Variable,USES
unZipIt,Method,Folder,Variable,USES
unZipIt,Method,Ze,Variable,USES
unZipIt,Method,Filename,Variable,USES
unZipIt,Method,Newfile,Variable,USES
unZipIt,Method,Len,Variable,USES
SprintProgress,Class,Ctx,Variable,HAS_FIELD
SprintProgress,Class,Authordata,Variable,HAS_FIELD
SprintProgress,Class,Almconfig,Variable,HAS_FIELD
SprintProgress,Class,Commonfunc,Variable,HAS_FIELD
SprintProgress,Class,getTaskRisk,Method,DECLARES
SprintProgress,Class,getIssueRiskEffortBased,Method,DECLARES
SprintProgress,Class,setRequiredValues,Method,DECLARES
SprintProgress,Class,getIssueRiskStoryPoint,Method,DECLARES
SprintProgress,Class,getLatestStoryPoints,Method,DECLARES
SprintProgress,Class,getIssueRiskStatus,Method,DECLARES
SprintProgress,Class,getInitialData,Method,DECLARES
SprintProgress,Class,getBurndown,Method,DECLARES
SprintProgress,Class,getBurndownEffort,Method,DECLARES
SprintProgress,Class,getBurndownStoryPoint,Method,DECLARES
LogDateComparator,Class,compare,Method,DECLARES
SprintComparator,Class,compare,Method,DECLARES
SprintProgress,Class,isAllocated,Method,DECLARES
SprintProgress,Class,isRemoved,Method,DECLARES
SprintProgressCalculations,Class,Ctx,Variable,HAS_FIELD
SprintProgressCalculations,Class,Metricrepo,Variable,HAS_FIELD
SprintProgressCalculations,Class,Mongo,Variable,HAS_FIELD
SprintProgressCalculations,Class,Alliterations,Variable,HAS_FIELD
SprintProgressCalculations,Class,Allissues,Variable,HAS_FIELD
SprintProgressCalculations,Class,Authordata,Variable,HAS_FIELD
SprintProgressCalculations,Class,Storydata,Variable,HAS_FIELD
SprintProgressCalculations,Class,Metricstaskdata,Variable,HAS_FIELD
SprintProgressCalculations,Class,Almconfig,Variable,HAS_FIELD
SprintProgressCalculations,Class,Iterationdata,Variable,HAS_FIELD
SprintProgressCalculations,Class,Almconfigrepo,Variable,HAS_FIELD
SprintProgressCalculations,Class,Logger,Variable,HAS_FIELD
SprintProgressCalculations,Class,getInititialDetails,Method,DECLARES
SprintProgressCalculations,Class,getissueBreakup,Method,DECLARES
SprintProgressCalculations,Class,getStoryProgress,Method,DECLARES
SprintProgressCalculations,Class,callOperation,Method,DECLARES
getInititialDetails,Method,Allissues,Variable,USES
getissueBreakup,Method,Response,Variable,USES
getissueBreakup,Method,Spdata,Variable,USES
getissueBreakup,Method,Componentlist,Variable,USES
getissueBreakup,Method,Metrics,Variable,USES
getissueBreakup,Method,Issuebreakuparr,Variable,USES
getissueBreakup,Method,Groupedbytype,Variable,USES
getissueBreakup,Method,Issuesbytype,Variable,USES
getissueBreakup,Method,Type,Variable,USES
getissueBreakup,Method,Sprints,Variable,USES
getissueBreakup,Method,States,Variable,USES
getissueBreakup,Method,Stateslist,Variable,USES
getissueBreakup,Method,Sprintinfo,Variable,USES
getissueBreakup,Method,Sprintname,Variable,USES
getissueBreakup,Method,Issuebysprintandtype,Variable,USES
getissueBreakup,Method,Countmap,Variable,USES
getissueBreakup,Method,Datalist,Variable,USES
getStoryProgress,Method,Filterstroydata,Variable,USES
getStoryProgress,Method,Filtermetricsdata,Variable,USES
getStoryProgress,Method,Response,Variable,USES
getStoryProgress,Method,Sprintlist,Variable,USES
getStoryProgress,Method,Storyprogressarr,Variable,USES
getStoryProgress,Method,Storytasks,Variable,USES
getStoryProgress,Method,Storypoints,Variable,USES
getStoryProgress,Method,Successpercentage,Variable,USES
getStoryProgress,Method,Spsize,Variable,USES
getStoryProgress,Method,Sorted,Variable,USES
getStoryProgress,Method,Successcount,Variable,USES
getStoryProgress,Method,Subtasklength,Variable,USES
getStoryProgress,Method,Tasklength,Variable,USES
StoryProgressModel,Class,Wid,Variable,HAS_FIELD
StoryProgressModel,Class,Storypoint,Variable,HAS_FIELD
StoryProgressModel,Class,Successpercentage,Variable,HAS_FIELD
StoryProgressModel,Class,getwId,Method,DECLARES
StoryProgressModel,Class,setwId,Method,DECLARES
StoryProgressModel,Class,getStoryPoint,Method,DECLARES
StoryProgressModel,Class,setStoryPoint,Method,DECLARES
setStoryPoint,Method,Storypoint,Variable,USES
StoryProgressModel,Class,getSuccessPercentage,Method,DECLARES
StoryProgressModel,Class,setSuccessPercentage,Method,DECLARES
setSuccessPercentage,Method,Successpercentage,Variable,USES
StoryProgressSprintwise,Class,Storytasks,Variable,HAS_FIELD
StoryProgressSprintwise,Class,Sprintname,Variable,HAS_FIELD
StoryProgressSprintwise,Class,getSprintName,Method,DECLARES
StoryProgressSprintwise,Class,setSprintName,Method,DECLARES
StoryProgressSprintwise,Class,getStoryTasks,Method,DECLARES
StoryProgressSprintwise,Class,setStoryTasks,Method,DECLARES
setStoryTasks,Method,Storytasks,Variable,USES
TaskRiskSprint,Class,Sprintname,Variable,HAS_FIELD
TaskRiskSprint,Class,Startdate,Variable,HAS_FIELD
TaskRiskSprint,Class,Enddate,Variable,HAS_FIELD
TaskRiskSprint,Class,Estimation,Variable,HAS_FIELD
TaskRiskSprint,Class,Completed,Variable,HAS_FIELD
TaskRiskSprint,Class,Assignewisedata,Variable,HAS_FIELD
TaskRiskSprint,Class,Assigneewisetasks,Variable,HAS_FIELD
TaskRiskSprint,Class,Issuecompletionpercentage,Variable,HAS_FIELD
TaskRiskSprint,Class,getAssigneeWiseTasks,Method,DECLARES
TaskRiskSprint,Class,setAssigneeWiseTasks,Method,DECLARES
setAssigneeWiseTasks,Method,Assigneewisetasks,Variable,USES
TaskRiskSprint,Class,getAssigneWiseData,Method,DECLARES
TaskRiskSprint,Class,setAssigneWiseData,Method,DECLARES
setAssigneWiseData,Method,Assignewisedata,Variable,USES
TaskRiskSprint,Class,getSprintName,Method,DECLARES
TaskRiskSprint,Class,setSprintName,Method,DECLARES
TaskRiskSprint,Class,getStartDate,Method,DECLARES
TaskRiskSprint,Class,setStartDate,Method,DECLARES
TaskRiskSprint,Class,getEndDate,Method,DECLARES
TaskRiskSprint,Class,setEndDate,Method,DECLARES
TaskRiskSprint,Class,getIssueCompletionPercentage,Method,DECLARES
TaskRiskSprint,Class,setIssueCompletionPercentage,Method,DECLARES
setIssueCompletionPercentage,Method,Issuecompletionpercentage,Variable,USES
TaskRiskSprint,Class,getEstimation,Method,DECLARES
TaskRiskSprint,Class,setEstimation,Method,DECLARES
setEstimation,Method,Estimation,Variable,USES
TaskRiskSprint,Class,getCompleted,Method,DECLARES
TaskRiskSprint,Class,setCompleted,Method,DECLARES
setCompleted,Method,Completed,Variable,USES
TeamQualityUtils,Class,calclulateAbsDiff,Method,DECLARES
TeamQualityUtils,Class,calclulateDiff,Method,DECLARES
TeamQualityUtils,Class,calculatePoints,Method,DECLARES
calculatePoints,Method,Diff,Variable,USES
TeamQualityUtils,Class,calculateAbsPoints,Method,DECLARES
calculateAbsPoints,Method,Abspoints,Variable,USES
VelocityCalculations,Class,Workingbacklog,Variable,HAS_FIELD
VelocityCalculations,Class,Velocityfields,Variable,HAS_FIELD
VelocityCalculations,Class,Closestates,Variable,HAS_FIELD
VelocityCalculations,Class,Tempsprefined,Variable,HAS_FIELD
VelocityCalculations,Class,Tempspremoved,Variable,HAS_FIELD
VelocityCalculations,Class,Storiescompleted,Variable,HAS_FIELD
VelocityCalculations,Class,Defetcscompleted,Variable,HAS_FIELD
VelocityCalculations,Class,Issuelist,Variable,HAS_FIELD
VelocityCalculations,Class,Workitemarr2,Variable,HAS_FIELD
VelocityCalculations,Class,Almconfig,Variable,HAS_FIELD
VelocityCalculations,Class,Scorecardsprintlist,Variable,HAS_FIELD
VelocityCalculations,Class,Refinedissulist,Variable,HAS_FIELD
VelocityCalculations,Class,Removedissulist,Variable,HAS_FIELD
VelocityCalculations,Class,Endatesprint,Variable,HAS_FIELD
VelocityCalculations,Class,calcVelocity,Method,DECLARES
VelocityCalculations,Class,callSPSpillOver,Method,DECLARES
VelocityCalculations,Class,callSP,Method,DECLARES
VelocityCalculations,Class,calcClosedSP,Method,DECLARES
VelocityCalculations,Class,storyLoop,Method,DECLARES
VelocityCalculations,Class,filterTrans,Method,DECLARES
VelocityCalculations,Class,storyLoopRefined,Method,DECLARES
VelocityCalculations,Class,checkRemoved,Method,DECLARES
VelocityCalculations,Class,checkWithdrawn,Method,DECLARES
Ctx,Variable,Portfolio,Variable,FLOWS_TO
Config,Variable,Jobkey,Variable,FLOWS_TO
Config,Variable,Job,Variable,FLOWS_TO
Config,Variable,Joba,Variable,FLOWS_TO
Config,Variable,Trigger,Variable,FLOWS_TO
Joba,Variable,Trigger,Variable,TRANSFORMS_TO
getDataFromTools,Method,Portfolioconfig,Table,READS_FROM
TriggerCollector,Class,Get:/Api/Triggercollector,Endpoint,EXPOSES
getDataFromTools,Method,Get:/Api/Triggercollector,Endpoint,MAPS_TO
getDataFromTools,Method,getContext,Method,CALLS
getDataFromTools,Method,Findall,Method,CALLS
getDataFromTools,Method,Checkexists,Method,CALLS
getDataFromTools,Method,Deletejob,Method,CALLS
getDataFromTools,Method,Info,Method,CALLS
getDataFromTools,Method,Newjob,Method,CALLS
getDataFromTools,Method,Put,Method,CALLS
getDataFromTools,Method,Newtrigger,Method,CALLS
getDataFromTools,Method,Cronschedule,Method,CALLS
getDataFromTools,Method,Schedulejob,Method,CALLS
getDataFromTools,Method,Start,Method,CALLS
Req,Variable,Todetailsaddsetting(Req),Variable,FLOWS_TO
Todetailsaddsetting(Req),Variable,Body,Variable,FLOWS_TO
Projectname,Variable,Retrievealmconfig(Projectname),Variable,FLOWS_TO
saveALMConfig,Method,Almconfiguration,Table,WRITES_TO
retrieveALMConfig,Method,Almconfiguration,Table,READS_FROM
ALMConfigController,Class,Post:/Almconfig,Endpoint,EXPOSES
ALMConfigController,Class,Get:/Almconfigdetails,Endpoint,EXPOSES
ALMConfigController,Class,Get:/Almconfigdetailsconfig,Endpoint,EXPOSES
Post:/Almconfig,Endpoint,Almconfigreq,Variable,ACCEPTS
Post:/Almconfig,Endpoint,Responseentity<Almconfiguration>,Variable,RETURNS
Get:/Almconfigdetails,Endpoint,Projectname,Variable,ACCEPTS
Get:/Almconfigdetails,Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS
Get:/Almconfigdetailsconfig,Endpoint,Projectname,Variable,ACCEPTS
Get:/Almconfigdetailsconfig,Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS
saveALMConfig,Method,toDetailsAddSetting,Method,CALLS
retrieveList,Method,retrieveALMConfig,Method,CALLS
Projname,Variable,"Getstoryageingdata(projname,Almtype)",Method,FLOWS_TO
Almtype,Variable,"Getstoryageingdata(projname,Almtype)",Method,FLOWS_TO
Projname,Variable,"Getgroomingtable(projname,Almtype)",Method,FLOWS_TO
Almtype,Variable,"Getgroomingtable(projname,Almtype)",Method,FLOWS_TO
Projname,Variable,Delduplicate(projname),Method,FLOWS_TO
Projname,Variable,"Getsprintprogresshome(projname,Almtype)",Method,FLOWS_TO
Almtype,Variable,"Getsprintprogresshome(projname,Almtype)",Method,FLOWS_TO
Projname,Variable,"Getdefectinsightdata(projname,Almtype,Componentflag)",Method,FLOWS_TO
Almtype,Variable,"Getdefectinsightdata(projname,Almtype,Componentflag)",Method,FLOWS_TO
Componentflag,Variable,"Getdefectinsightdata(projname,Almtype,Componentflag)",Method,FLOWS_TO
Projname,Variable,"Getdefecttrendandclassification(projname,Almtype,Componentbased)",Method,FLOWS_TO
Almtype,Variable,"Getdefecttrendandclassification(projname,Almtype,Componentbased)",Method,FLOWS_TO
Componentbased,Variable,"Getdefecttrendandclassification(projname,Almtype,Componentbased)",Method,FLOWS_TO
getAlmType,Method,Configurationsetting,Table,READS_FROM
Req,Variable,Details,Variable,TRANSFORMS_TO
toDetailsAddSetting,Method,Details,Variable,PRODUCES
toDetailsAddSetting,Method,Copyproperties,Method,CALLS
ALMConfigReq,Class,N/A,Endpoint,EXPOSES
toDetailsAddSetting,Method,N/A,Endpoint,MAPS_TO
getAlmType,Method,Almtype,Variable,PRODUCES
setAlmType,Method,Almtype,Variable,CALLS
getAlmType,Method,Almtype,Variable,CALLS
Username,Variable,Httpauthenticationfeature,Variable,FLOWS_TO
Password,Variable,Httpauthenticationfeature,Variable,FLOWS_TO
Httpauthenticationfeature,Variable,Client,Variable,FLOWS_TO
Client,Variable,Webtarget,Variable,FLOWS_TO
Webtarget,Variable,Invocationbuilder,Variable,FLOWS_TO
Invocationbuilder,Variable,Response,Variable,PRODUCES
Authentication,Class,Get:/Api/Tfsbuild,Endpoint,EXPOSES
Authenticate,Method,Get:/Api/Tfsbuild,Endpoint,MAPS_TO
Get:/Api/Tfsbuild,Endpoint,Proname,Variable,ACCEPTS
Get:/Api/Tfsbuild,Endpoint,Response,Variable,RETURNS
Authenticationofservice,Method,Authenticate,Method,CALLS
Authenticate,Method,Uri:Http://Localhost:8081/Api/Tfsbuild,Externalservice,INVOKES
Constructor,Method,Lastupdated,Variable,PRODUCES
Lastupdated,Variable,getLastUpdated,Method,FLOWS_TO
Retrospective,Variable,Almconfiguration,Variable,FLOWS_TO
saveALMConfig,Method,Almconfiguration,Variable,PRODUCES
retrieveALMConfig,Method,Dataresponse<Almconfiguration>,Variable,PRODUCES
Almconfigservice,Class,Post:/Api/Alm-Config,Endpoint,EXPOSES
Almconfigservice,Class,Get:/Api/Alm-Config/{Projectname},Endpoint,EXPOSES
saveALMConfig,Method,Post:/Api/Alm-Config,Endpoint,MAPS_TO
retrieveALMConfig,Method,Get:/Api/Alm-Config/{Projectname},Endpoint,MAPS_TO
Post:/Api/Alm-Config,Endpoint,Almconfiguration,Variable,ACCEPTS
Post:/Api/Alm-Config,Endpoint,Almconfiguration,Variable,RETURNS
Get:/Api/Alm-Config/{Projectname},Endpoint,Projectname,Variable,ACCEPTS
Get:/Api/Alm-Config/{Projectname},Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS
retrieveALMConfig,Method,saveALMConfig,Method,CALLS
Req,Variable,Getprojectname()),Variable,FLOWS_TO
Getprojectname(),Variable,Getprojectname()),Variable,FLOWS_TO
Req,Variable,Save(Req),Variable,FLOWS_TO
Save(Req),Variable,Almconfiguration,Variable,TRANSFORMS_TO
Projectname,Variable,Findbyprojectname(Projectname),Variable,FLOWS_TO
Getprojectname()),Variable,Almconfiguration,Table,READS_FROM
Getprojectname()),Variable,Almconfiguration,Table,WRITES_TO
Req,Variable,Almconfiguration,Table,PERSISTS_TO
Findbyprojectname(Projectname),Variable,Almconfiguration,Table,READS_FROM
ALMConfigServiceImplementation,Class,Post:/Api/Config/Save,Endpoint,EXPOSES
ALMConfigServiceImplementation,Class,Get:/Api/Config/Retrieve,Endpoint,EXPOSES
saveALMConfig,Method,Post:/Api/Config/Save,Endpoint,MAPS_TO
retrieveALMConfig,Method,Get:/Api/Config/Retrieve,Endpoint,MAPS_TO
Post:/Api/Config/Save,Endpoint,Req,Variable,ACCEPTS
Post:/Api/Config/Save,Endpoint,Almconfiguration,Variable,RETURNS
Get:/Api/Config/Retrieve,Endpoint,Projectname,Variable,ACCEPTS
Get:/Api/Config/Retrieve,Endpoint,Dataresponse<Almconfiguration>,Variable,RETURNS
saveALMConfig,Method,findByProjectName,Method,CALLS
saveALMConfig,Method,deleteByProjectName,Method,CALLS
saveALMConfig,Method,Save,Method,CALLS
retrieveALMConfig,Method,findByProjectName,Method,CALLS
Wid,Variable,Monogoutmetrics,Variable,FLOWS_TO
Pname,Variable,List<Monogoutmetrics>,Variable,FLOWS_TO
Wid,Variable,List<Changehistorymodel>,Variable,FLOWS_TO
Pname,Variable,"Map<Integer, List<Iterationoutmodel>>",Variable,FLOWS_TO
Almservice,Class,Get:/Api/Getmetricdetails,Endpoint,EXPOSES
getMetricDetails,Method,Get:/Api/Getmetricdetails,Endpoint,MAPS_TO
getAllMetrics,Method,Get:/Api/Getallmetrics,Endpoint,MAPS_TO
getChangesItems,Method,Get:/Api/Getchangesitems,Endpoint,MAPS_TO
getCurrentProjectDetails,Method,Get:/Api/Getcurrentprojectdetails,Endpoint,MAPS_TO
Get:/Api/Getmetricdetails,Endpoint,Wid,Variable,ACCEPTS
Get:/Api/Getallmetrics,Endpoint,Pname,Variable,ACCEPTS
Get:/Api/Getmetricdetails,Endpoint,Monogoutmetrics,Variable,RETURNS
Get:/Api/Getallmetrics,Endpoint,List<Monogoutmetrics>,Variable,RETURNS
Get:/Api/Getchangesitems,Endpoint,List<Changehistorymodel>,Variable,RETURNS
Get:/Api/Getcurrentprojectdetails,Endpoint,"Map<Integer, List<Iterationoutmodel>>",Variable,RETURNS
getAllMetrics,Method,getMetricDetails,Method,CALLS
Mongotemplate,Variable,Response,Variable,PRODUCES
getUnReleaseData,Method,Response,Variable,PRODUCES
getProdDefects,Method,Response,Variable,PRODUCES
getDateIterations,Method,Response,Variable,PRODUCES
getDefects,Method,Response,Variable,PRODUCES
getSlaData,Method,Response,Variable,PRODUCES
Iterationrepo,Variable,Response,Variable,PRODUCES
getActiveSprints,Method,Response,Variable,PRODUCES
getAllIterations,Method,Response,Variable,PRODUCES
Authorrepo,Variable,Datamodel,Variable,TRANSFORMS_TO
populateAuthor,Method,Datamodel,Variable,PRODUCES
Releaserepo,Variable,Releases,Variable,TRANSFORMS_TO
getRelease,Method,Releases,Variable,PRODUCES
Metricrepo,Variable,Allmetrics,Variable,TRANSFORMS_TO
getIssueHierarchy,Method,Allmetrics,Variable,PRODUCES
getMetricDetails,Method,Mongometrics,Table,READS_FROM
getChangesItems,Method,Changehistory,Table,READS_FROM
Req,Variable,Configurationsetting,Variable,TRANSFORMS_TO
getConfig,Method,Configurationsetting,Table,READS_FROM
Req,Variable,Configurationsetting,Table,PERSISTS_TO
deleteConfig,Method,Configurationsetting,Table,WRITES_TO
deleteAllCollections,Method,Configurationsetting,Table,WRITES_TO
deleteProject,Method,Configurationsetting,Table,WRITES_TO
getConfigProject,Method,Configurationsetting,Table,READS_FROM
Configurationsettingservice,Class,Get:/Api/Configurations,Endpoint,EXPOSES
Configurationsettingservice,Class,Post:/Api/Configurations,Endpoint,EXPOSES
Configurationsettingservice,Class,Delete:/Api/Configurations,Endpoint,EXPOSES
getConfig,Method,Get:/Api/Configurations,Endpoint,MAPS_TO
addConfig,Method,Post:/Api/Configurations,Endpoint,MAPS_TO
deleteConfig,Method,Delete:/Api/Configurations,Endpoint,MAPS_TO
deleteAllCollections,Method,Delete:/Api/Collections,Endpoint,MAPS_TO
deleteProject,Method,Delete:/Api/Projects,Endpoint,MAPS_TO
getConfigProject,Method,Get:/Api/Projects/{Projectname},Endpoint,MAPS_TO
Post:/Api/Configurations,Endpoint,Req,Variable,ACCEPTS
Get:/Api/Configurations,Endpoint,Dataresponse<Iterable<Configurationsetting>>,Variable,RETURNS
Post:/Api/Configurations,Endpoint,Configurationsetting,Variable,RETURNS
Get:/Api/Projects/{Projectname},Endpoint,Configurationsetting,Variable,RETURNS
Delete:/Api/Configurations,Endpoint,Configurationsetting,Variable,ACCEPTS
Delete:/Api/Collections,Endpoint,Projectname,Variable,ACCEPTS
Delete:/Api/Projects,Endpoint,Projectname,Variable,ACCEPTS
getConfig,Method,Getconfigurations,Method,CALLS
addConfig,Method,Saveconfiguration,Method,CALLS
deleteConfig,Method,Removeconfiguration,Method,CALLS
deleteAllCollections,Method,Clearallcollections,Method,CALLS
deleteProject,Method,Removeproject,Method,CALLS
getConfigProject,Method,Fetchprojectconfig,Method,CALLS
deleteProject,Method,Projectmanagementapi,Externalservice,INVOKES
Date,Variable,Timestamp,Variable,FLOWS_TO
Failurepattern,Variable,Failurepatternobj,Variable,FLOWS_TO
addConfig,Method,Configurationsetting,Table,READS_FROM
addConfig,Method,Configurationsetting,Table,WRITES_TO
deleteAllCollections,Method,Buildfailurepatternforproject,Table,READS_FROM
deleteAllCollections,Method,Buildfailurepatternforproject,Table,WRITES_TO
deleteAllCollections,Method,Buildtool,Table,WRITES_TO
deleteAllCollections,Method,Codecoverage,Table,WRITES_TO
deleteAllCollections,Method,Codequality,Table,WRITES_TO
deleteAllCollections,Method,Healthdata,Table,WRITES_TO
deleteAllCollections,Method,Scmtool,Table,WRITES_TO
deleteProject,Method,Almconfig,Table,READS_FROM
deleteProject,Method,Almconfig,Table,WRITES_TO
deleteProject,Method,Buildfailurepatternforproject,Table,WRITES_TO
deleteProject,Method,Goalsetting,Table,WRITES_TO
deleteProject,Method,Portfolioconfig,Table,WRITES_TO
deleteProject,Method,Projecthealth,Table,WRITES_TO
deleteProject,Method,Userassociation,Table,WRITES_TO
deleteProject,Method,Chartconfig,Table,WRITES_TO
ConfigurationSettingServiceImplementation,Class,Get:/Api/Configurations,Endpoint,EXPOSES
addConfig,Method,Req,Variable,ACCEPTS
addConfig,Method,Req,Variable,RETURNS
deleteConfig,Method,Configurationsetting,Variable,ACCEPTS
deleteConfig,Method,Int,Variable,RETURNS
deleteAllCollections,Method,Delete:/Api/All_Collections,Endpoint,MAPS_TO
deleteAllCollections,Method,Projectname,Variable,ACCEPTS
deleteProject,Method,Delete:/Api/Project,Endpoint,MAPS_TO
deleteProject,Method,Projectname,Variable,ACCEPTS
getConfigProject,Method,Get:/Api/Configurations/{Projectname},Endpoint,MAPS_TO
getConfigProject,Method,Pname,Variable,ACCEPTS
getConfigProject,Method,Config,Variable,RETURNS
addConfig,Method,deleteConfig,Method,CALLS
deleteAllCollections,Method,Almservice.Delallissues,Externalservice,INVOKES
getConfigProject,Method,Encryptiondecryptionaes.Decrypt,Externalservice,INVOKES
Format,Variable,Pattern,Variable,FLOWS_TO
Pattern,Variable,Simpledateformat,Variable,FLOWS_TO
Dateinput,Variable,Simpledateformat,Variable,FLOWS_TO
Simpledateformat,Variable,Formatteddate,Variable,TRANSFORMS_TO
"""Yyyy/Mm/Dd""",Variable,Format,Variable,FLOWS_TO
Start,Variable,Startformatted,Variable,TRANSFORMS_TO
End,Variable,Endformatted,Variable,TRANSFORMS_TO
Startformatted,Variable,"Map[""Start""]",Variable,FLOWS_TO
Endformatted,Variable,"Map[""End""]",Variable,FLOWS_TO
getLastWeekWorkingDateRange,Method,getDateInFormat,Method,CALLS
getDateInFormat,Method,Formatteddate,Variable,PRODUCES
Date,Variable,Daysago,Variable,TRANSFORMS_TO
Daysago,Variable,Time,Variable,TRANSFORMS_TO
Ctx,Variable,Getcontext,Variable,PRODUCES
Executor,Variable,Newfixedthreadpool(10),Variable,PRODUCES
Threadgroup,Variable,Newcachedthreadpool,Variable,PRODUCES
Childthreadgroup,Variable,Newcachedthreadpool,Variable,PRODUCES
Configs,Variable,Findbyprojectname,Variable,PRODUCES
Config,Variable,Size() - 1),Variable,TRANSFORMS_TO
Configurationcolection,Variable,Findbyprojectname,Variable,PRODUCES
Listoftools,Variable,Getmetrics,Variable,TRANSFORMS_TO
Metric1,Variable,Next,Variable,TRANSFORMS_TO
Smtpfieldsarray,Variable,Mailsetuprepo.findall,Table,PERSISTS_TO
multiTaskThread,Method,Portfolioconfig,Table,READS_FROM
multiTaskThread,Method,Configurationsetting,Table,READS_FROM
getSMTPInfo,Method,Mailsetup,Table,READS_FROM
ProjectCollector,Class,Post:/Api/Project-Collector,Endpoint,EXPOSES
multiTaskThread,Method,Post:/Api/Project-Collector,Endpoint,MAPS_TO
Post:/Api/Project-Collector,Endpoint,Projectname,Variable,ACCEPTS
Post:/Api/Project-Collector,Endpoint,Executionstatus,Variable,RETURNS
ProjectCollector,Class,getContext,Method,CALLS
multiTaskThread,Method,findByProjectName,Method,CALLS
multiTaskThread,Method,makeSwitchCaseCall,Method,CALLS
multiTaskThread,Method,destroy,Method,CALLS
makeSwitchCaseCall,Method,jenkins,Method,CALLS
makeSwitchCaseCall,Method,circleCI,Method,CALLS
makeSwitchCaseCall,Method,sonarqube,Method,CALLS
destroy,Method,projectHealth,Method,CALLS
destroy,Method,sprintComparison,Method,CALLS
destroy,Method,evictAllCaches,Method,CALLS
sendMail,Method,Send,Method,CALLS
"""Jenkins""",Variable,jenkins,Method,CALLS
"""Circleci""",Variable,circleCI,Method,CALLS
"""Sonarqube""",Variable,sonarqube,Method,CALLS
"""Bitbucket""",Variable,bit,Method,CALLS
"""Code Coverage""",Variable,codecoverage,Method,CALLS
"""Jira""",Variable,jira,Method,CALLS
"""Teamcity""",Variable,teamcity,Method,CALLS
"""Tfs Build""",Variable,tfsbuild,Method,CALLS
"""Tfs""",Variable,tfsversion,Method,CALLS
"""Bit Server""",Variable,bitserver,Method,CALLS
"""Octopus Deploy""",Variable,octopusDeploy,Method,CALLS
"""Azure Repo""",Variable,azureRepo,Method,CALLS
"""Github Action""",Variable,githubAction,Method,CALLS
Dbuserid,Variable,Userid,Variable,FLOWS_TO
Dbpassword,Variable,Password,Variable,FLOWS_TO
mongo,Method,Client,Variable,PRODUCES
mongoTemplate,Method,Mongotemplate,Variable,PRODUCES
getDatabaseName,Method,Db,Table,READS_FROM
mongoTemplate,Method,Mongotemplate,Table,WRITES_TO
mongo,Method,Mongoclient,Externalservice,INVOKES
mongo,Method,Mongocredential,Externalservice,INVOKES
getInstance,Method,Constructor,Method,CALLS
mongoTemplate,Method,mongo,Method,CALLS
mongoTemplate,Method,getDatabaseName,Method,CALLS
mongo,Method,Getresourceasstream,Method,CALLS
mongo,Method,Load,Method,CALLS
mongo,Method,Client=NewMongoclient,Method,CALLS
getContext,Method,Annotationconfigapplicationcontext,Method,CALLS
Transitionmodel,Variable,Transitionlist,Variable,FLOWS_TO
Transitionmodel,Variable,Transitiongrouped,Variable,FLOWS_TO
Efforthistorymodel,Variable,Efforthistorylist,Variable,FLOWS_TO
Efforthistorymodel,Variable,Effortsgrouped,Variable,FLOWS_TO
Metricsmodel,Variable,Metriclist,Variable,FLOWS_TO
Metricsmodel,Variable,Monogoutmetrics,Variable,TRANSFORMS_TO
Monogoutmetrics,Variable,Aggregatedmetriclist,Variable,FLOWS_TO
Monogoutmetrics,Variable,Aggregatedmetriclistgrouped,Variable,FLOWS_TO
Iterationmodel,Variable,Iterationlist,Variable,FLOWS_TO
Iterationmodel,Variable,Iterationoutmodel,Variable,TRANSFORMS_TO
Iterationoutmodel,Variable,Aggreagation,Variable,FLOWS_TO
Iterationoutmodel,Variable,Aggregationitr,Variable,FLOWS_TO
findByPNameAndPAlmType,Method,Metric,Table,READS_FROM
findByPName,Method,Transition,Table,READS_FROM
findByPName,Method,Efforthistory,Table,READS_FROM
findByPName,Method,Iteration,Table,READS_FROM
aggregate,Method,Metrics,Table,READS_FROM
aggregate,Method,Efforthistory,Table,READS_FROM
aggregate,Method,Transition,Table,READS_FROM
Count,Method,Author,Table,READS_FROM
Count,Method,Build,Table,READS_FROM
Save,Method,Metric,Table,WRITES_TO
Save,Method,Iteration,Table,WRITES_TO
MongoAggregate,Class,Aggregatemetrics,Endpoint,EXPOSES
aggregateMetrics,Method,Get:/Api/Aggregatemetrics,Endpoint,MAPS_TO
aggregate,Method,Post:/Api/Aggregate,Endpoint,MAPS_TO
getMetric,Method,Get:/Api/Getmetric,Endpoint,MAPS_TO
getCurrentItr,Method,Get:/Api/Getcurrentitr,Endpoint,MAPS_TO
deleteIssues,Method,Delete:/Api/Deleteissues,Endpoint,MAPS_TO
updateComponentForTaskAndSubtasks,Method,Put:/Api/Updatecomponents,Endpoint,MAPS_TO
aggregateMetrics,Method,findByPNameAndPAlmType,Method,CALLS
aggregateMetrics,Method,findByPName,Method,CALLS
aggregateMetrics,Method,Copyproperties,Method,CALLS
aggregate,Method,findByPNameAndPAlmType,Method,CALLS
aggregate,Method,findByPName,Method,CALLS
aggregate,Method,Copyproperties,Method,CALLS
aggregate,Method,Save,Method,CALLS
getMetric,Method,aggregate,Method,CALLS
getCurrentItr,Method,aggregate,Method,CALLS
deleteIssues,Method,Remove,Method,CALLS
updateComponentForTaskAndSubtasks,Method,findByPNameAndType,Method,CALLS
updateComponentForTaskAndSubtasks,Method,findByPNameAndWId,Method,CALLS
updateComponentForTaskAndSubtasks,Method,Save,Method,CALLS
updateComponentForTaskAndSubtasks,Method,Delete,Method,CALLS
ALMConfiguration,Class,None,Endpoint,EXPOSES
getId,Method,Id,Variable,PRODUCES
setId,Method,Id,Variable,FLOWS_TO
Id,Variable,Basemodel,Table,PERSISTS_TO
Projkey,Variable,Changeitems,Table,PERSISTS_TO
Pname,Variable,Changeitems,Table,PERSISTS_TO
Wid,Variable,Changeitems,Table,PERSISTS_TO
Field,Variable,Changeitems,Table,PERSISTS_TO
Oldvalue,Variable,Changeitems,Table,PERSISTS_TO
Newvalue,Variable,Changeitems,Table,PERSISTS_TO
Date,Variable,Changeitems,Table,PERSISTS_TO
Component,Variable,Getcomponent(),Variable,FLOWS_TO
Component,Variable,Setcomponent(String Component),Variable,FLOWS_TO
Velocitylist,Variable,Getvelocitylist(),Variable,FLOWS_TO
Velocitylist,Variable,Setvelocitylist(List<Scorecardsprintdata> V),Variable,FLOWS_TO
Getcomponent(),Method,InternalGetterMechanism,Method,CALLS
Setcomponent(stringComponent),Method,InternalSetterMechanism,Method,CALLS
Getvelocitylist(),Method,InternalGetterMechanism,Method,CALLS
Setvelocitylist(list<scorecardsprintdata>V),Method,InternalSetterMechanism,Method,CALLS
False,Variable,Manualdata,Variable,TRANSFORMS_TO
ConfigurationSetting,Class,Configuration,Table,PERSISTS_TO
Constructor,Method,Toolname,Variable,FLOWS_TO
Constructor,Method,Manualdata,Variable,FLOWS_TO
setToolName,Method,Toolname,Variable,FLOWS_TO
setURL,Method,Url,Variable,FLOWS_TO
setUserName,Method,Username,Variable,FLOWS_TO
setPassword,Method,Password,Variable,FLOWS_TO
setToolType,Method,Tooltype,Variable,FLOWS_TO
setWidgetName,Method,Widgetname,Variable,FLOWS_TO
setJobName,Method,Jobname,Variable,FLOWS_TO
setProjectCode,Method,Projectcode,Variable,FLOWS_TO
setSelected,Method,Selected,Variable,FLOWS_TO
setDomain,Method,Domain,Variable,FLOWS_TO
setHost,Method,Host,Variable,FLOWS_TO
setPort,Method,Port,Variable,FLOWS_TO
setDbType,Method,Dbtype,Variable,FLOWS_TO
setSchema,Method,Schema,Variable,FLOWS_TO
setRepoName,Method,Reponame,Variable,FLOWS_TO
setSecret,Method,Secret,Variable,FLOWS_TO
Name,Variable,Getname(),Method,FLOWS_TO
Getname(),Method,Name,Variable,FLOWS_TO
Name,Variable,Setname(name),Method,FLOWS_TO
Setname(name),Method,Name,Variable,TRANSFORMS_TO
Getname(),Method,Name,Variable,CALLS
Setname(name),Method,Name,Variable,CALLS
IterationModel,Class,Iterations,Table,PERSISTS_TO
compareTo,Method,getStDate,Method,CALLS
Timestamp,Variable,Long,Variable,TRANSFORMS_TO
Stdate,Variable,Long,Variable,TRANSFORMS_TO
Enddate,Variable,Long,Variable,TRANSFORMS_TO
Velocity,Variable,Int,Variable,TRANSFORMS_TO
Reldate,Variable,Long,Variable,TRANSFORMS_TO
Totdefects,Variable,Int,Variable,TRANSFORMS_TO
Totcloseddefects,Variable,Int,Variable,TRANSFORMS_TO
Totopendefetct,Variable,Int,Variable,TRANSFORMS_TO
Sid,Variable,Int,Variable,TRANSFORMS_TO
Completeddate,Variable,Long,Variable,TRANSFORMS_TO
Totalstorypoints,Variable,Double,Variable,TRANSFORMS_TO
Closedstories,Variable,Int,Variable,TRANSFORMS_TO
IterationOutModel,Class,Author,Table,PERSISTS_TO
getActEst,Method,Actest,Variable,PRODUCES
getAffectedVersions,Method,Affectedversions,Variable,PRODUCES
getAllocatedDate,Method,Allocateddate,Variable,PRODUCES
getAssgnTo,Method,Assgnto,Variable,PRODUCES
getBaseline,Method,Baseline,Variable,PRODUCES
getComponents,Method,Components,Variable,PRODUCES
getCreateDate,Method,Createdate,Variable,PRODUCES
getCycleTime,Method,Cycletime,Variable,PRODUCES
getDefectInjector,Method,Defectinjector,Variable,PRODUCES
getDoneDate,Method,Donedate,Variable,PRODUCES
getEffort,Method,Effort,Variable,PRODUCES
getEpicLink,Method,Epiclink,Variable,PRODUCES
getEstChange,Method,Estchange,Variable,PRODUCES
getExtEffort,Method,Exteffort,Variable,PRODUCES
getFixVer,Method,Fixver,Variable,PRODUCES
getInWardIssueLink,Method,Inwardissuelink,Variable,PRODUCES
getLeadTime,Method,Leadtime,Variable,PRODUCES
getOrgEst,Method,Orgest,Variable,PRODUCES
getOutWardIssueLink,Method,Outwardissuelink,Variable,PRODUCES
getpAlmType,Method,Palmtype,Variable,PRODUCES
getpName,Method,Pname,Variable,PRODUCES
getPriority,Method,Priority,Variable,PRODUCES
getRemTime,Method,Remtime,Variable,PRODUCES
getResDate,Method,Resdate,Variable,PRODUCES
getSeverity,Method,Severity,Variable,PRODUCES
getsId,Method,Sid,Variable,PRODUCES
getsName,Method,Sname,Variable,PRODUCES
getState,Method,State,Variable,PRODUCES
getStateSet,Method,Stateset,Variable,PRODUCES
getStatusCategory,Method,Statuscategory,Variable,PRODUCES
getStoryPoints,Method,Storypoints,Variable,PRODUCES
getSubtaskList,Method,Subtasklist,Variable,PRODUCES
getSumm,Method,Summ,Variable,PRODUCES
getTaskList,Method,Tasklist,Variable,PRODUCES
getType,Method,Type,Variable,PRODUCES
getUpdatedDate,Method,Updateddate,Variable,PRODUCES
getUpdDate,Method,Upddate,Variable,PRODUCES
getWaitTime,Method,Waittime,Variable,PRODUCES
getwId,Method,Wid,Variable,PRODUCES
getTargetRelease,Method,Targetrelease,Variable,PRODUCES
getSquads,Method,Squads,Variable,PRODUCES
getCategory,Method,Category,Variable,PRODUCES
getWhenFound,Method,Whenfound,Variable,PRODUCES
getWhereFound,Method,Wherefound,Variable,PRODUCES
getHowFound,Method,Howfound,Variable,PRODUCES
getEpicIssues,Method,Epicissues,Variable,PRODUCES
getProjKey,Method,Projkey,Variable,PRODUCES
getMultiSprints,Method,Multisprints,Variable,PRODUCES
getEnvironment,Method,Environment,Variable,PRODUCES
getAcFeatureOrCapability,Method,Acfeatureorcapability,Variable,PRODUCES
getURL,Method,Url,Variable,PRODUCES
getAcFeatureId,Method,Acfeatureid,Variable,PRODUCES
getRallyRefURL,Method,Rallyrefurl,Variable,PRODUCES
getCustomFields,Method,Customfields,Variable,PRODUCES
Actest,Variable,setActEst,Method,FLOWS_TO
Affectedversions,Variable,setAffectedVersions,Method,FLOWS_TO
Allocateddate,Variable,setAllocatedDate,Method,FLOWS_TO
Assgnto,Variable,setAssgnTo,Method,FLOWS_TO
Baseline,Variable,setBaseLine,Method,FLOWS_TO
Components,Variable,setComponents,Method,FLOWS_TO
Createdate,Variable,setCreateDate,Method,FLOWS_TO
Cycletime,Variable,setCycleTime,Method,FLOWS_TO
Defectinjector,Variable,setDefectInjector,Method,FLOWS_TO
Donedate,Variable,setDoneDate,Method,FLOWS_TO
Effort,Variable,setEffort,Method,FLOWS_TO
Epiclink,Variable,setEpicLink,Method,FLOWS_TO
Estchange,Variable,setEstChange,Method,FLOWS_TO
Exteffort,Variable,setExtEffort,Method,FLOWS_TO
Fixver,Variable,setFixVer,Method,FLOWS_TO
Inwardissuelink,Variable,setInWardIssueLink,Method,FLOWS_TO
Leadtime,Variable,setLeadTime,Method,FLOWS_TO
Orgest,Variable,setOrgEst,Method,FLOWS_TO
Outwardissuelink,Variable,setOutWardIssueLink,Method,FLOWS_TO
Palmtype,Variable,setpAlmType,Method,FLOWS_TO
Pname,Variable,setpName,Method,FLOWS_TO
Priority,Variable,setPriority,Method,FLOWS_TO
Remtime,Variable,setRemTime,Method,FLOWS_TO
Resdate,Variable,setResDate,Method,FLOWS_TO
Severity,Variable,setSeverity,Method,FLOWS_TO
Sid,Variable,setsId,Method,FLOWS_TO
Sname,Variable,setsName,Method,FLOWS_TO
State,Variable,setState,Method,FLOWS_TO
Stateset,Variable,setStateSet,Method,FLOWS_TO
Statuscategory,Variable,setStatusCategory,Method,FLOWS_TO
Storypoints,Variable,setStoryPoints,Method,FLOWS_TO
Subtasklist,Variable,setSubtaskList,Method,FLOWS_TO
Summ,Variable,setSumm,Method,FLOWS_TO
Tasklist,Variable,setTaskList,Method,FLOWS_TO
Type,Variable,setType,Method,FLOWS_TO
Updateddate,Variable,setUpdatedDate,Method,FLOWS_TO
Upddate,Variable,setUpdDate,Method,FLOWS_TO
Waittime,Variable,setWaitTime,Method,FLOWS_TO
Wid,Variable,setwId,Method,FLOWS_TO
Targetrelease,Variable,setTargetRelease,Method,FLOWS_TO
Squads,Variable,setSquads,Method,FLOWS_TO
Category,Variable,setCategory,Method,FLOWS_TO
Whenfound,Variable,setWhenFound,Method,FLOWS_TO
Wherefound,Variable,setWhereFound,Method,FLOWS_TO
Howfound,Variable,setHowFound,Method,FLOWS_TO
Epicissues,Variable,setEpicIssues,Method,FLOWS_TO
Projkey,Variable,setProjKey,Method,FLOWS_TO
Multisprints,Variable,setMultiSprints,Method,FLOWS_TO
Environment,Variable,setEnvironment,Method,FLOWS_TO
Acfeatureorcapability,Variable,setAcFeatureOrCapability,Method,FLOWS_TO
Url,Variable,setURL,Method,FLOWS_TO
Acfeatureid,Variable,setAcFeatureId,Method,FLOWS_TO
Rallyrefurl,Variable,setRallyRefURL,Method,FLOWS_TO
Customfields,Variable,setCustomFields,Method,FLOWS_TO
MetricsModel,Class,Metrics,Table,PERSISTS_TO
Actest,Variable,getActEst,Method,FLOWS_TO
Affectedversions,Variable,getAffectedVersions,Method,FLOWS_TO
Allocateddate,Variable,getAllocatedDate,Method,FLOWS_TO
Assgnto,Variable,getAssgnTo,Method,FLOWS_TO
Baseline,Variable,getBaseline,Method,FLOWS_TO
Components,Variable,getComponents,Method,FLOWS_TO
Createdate,Variable,getCreateDate,Method,FLOWS_TO
Cycletime,Variable,getCycleTime,Method,FLOWS_TO
Defectinjector,Variable,getDefectInjector,Method,FLOWS_TO
Donedate,Variable,getDoneDate,Method,FLOWS_TO
Effort,Variable,getEffort,Method,FLOWS_TO
Efforts,Variable,getEfforts,Method,FLOWS_TO
Epiclink,Variable,getEpicLink,Method,FLOWS_TO
Estchange,Variable,getEstChange,Method,FLOWS_TO
Exteffort,Variable,getExtEffort,Method,FLOWS_TO
Fixver,Variable,getFixVer,Method,FLOWS_TO
Inwardissuelink,Variable,getInWardIssueLink,Method,FLOWS_TO
Leadtime,Variable,getLeadTime,Method,FLOWS_TO
Orgest,Variable,getOrgEst,Method,FLOWS_TO
Outwardissuelink,Variable,getOutWardIssueLink,Method,FLOWS_TO
Palmtype,Variable,getpAlmType,Method,FLOWS_TO
Pname,Variable,getpName,Method,FLOWS_TO
Priority,Variable,getPriority,Method,FLOWS_TO
Remtime,Variable,getRemTime,Method,FLOWS_TO
Resdate,Variable,getResDate,Method,FLOWS_TO
Severity,Variable,getSeverity,Method,FLOWS_TO
Sid,Variable,getsId,Method,FLOWS_TO
Sname,Variable,getsName,Method,FLOWS_TO
State,Variable,getState,Method,FLOWS_TO
Stateset,Variable,getStateSet,Method,FLOWS_TO
Statuscategory,Variable,getStatusCategory,Method,FLOWS_TO
Storypoints,Variable,getStoryPoints,Method,FLOWS_TO
Subtasklist,Variable,getSubtaskList,Method,FLOWS_TO
Summ,Variable,getSumm,Method,FLOWS_TO
Tasklist,Variable,getTaskList,Method,FLOWS_TO
Transitions,Variable,getTransitions,Method,FLOWS_TO
Type,Variable,getType,Method,FLOWS_TO
Updateddate,Variable,getUpdatedDate,Method,FLOWS_TO
Upddate,Variable,getUpdDate,Method,FLOWS_TO
Waittime,Variable,getWaitTime,Method,FLOWS_TO
Wid,Variable,getwId,Method,FLOWS_TO
Targetrelease,Variable,getTargetRelease,Method,FLOWS_TO
Squads,Variable,getSquads,Method,FLOWS_TO
Category,Variable,getCategory,Method,FLOWS_TO
Whenfound,Variable,getWhenFound,Method,FLOWS_TO
Wherefound,Variable,getWhereFound,Method,FLOWS_TO
Howfound,Variable,getHowFound,Method,FLOWS_TO
Epicissues,Variable,getEpicIssues,Method,FLOWS_TO
Projkey,Variable,getProjKey,Method,FLOWS_TO
Multisprints,Variable,getMultiSprints,Method,FLOWS_TO
Environment,Variable,getEnvironment,Method,FLOWS_TO
Acfeatureorcapability,Variable,getAcFeatureOrCapability,Method,FLOWS_TO
Acfeatureid,Variable,getAcFeatureId,Method,FLOWS_TO
Rallyrefurl,Variable,getRallyRefURL,Method,FLOWS_TO
Label,Variable,getLabel,Method,FLOWS_TO
getActEst,Method,Actest,Variable,FLOWS_TO
getAffectedVersions,Method,Affectedversions,Variable,FLOWS_TO
getAllocatedDate,Method,Allocateddate,Variable,FLOWS_TO
getAssgnTo,Method,Assgnto,Variable,FLOWS_TO
getBaseline,Method,Baseline,Variable,FLOWS_TO
getComponents,Method,Components,Variable,FLOWS_TO
getCreateDate,Method,Createdate,Variable,FLOWS_TO
getCycleTime,Method,Cycletime,Variable,FLOWS_TO
getDefectInjector,Method,Defectinjector,Variable,FLOWS_TO
getDoneDate,Method,Donedate,Variable,FLOWS_TO
getEffort,Method,Effort,Variable,FLOWS_TO
getEfforts,Method,Efforts,Variable,FLOWS_TO
getEpicLink,Method,Epiclink,Variable,FLOWS_TO
getEstChange,Method,Estchange,Variable,FLOWS_TO
getExtEffort,Method,Exteffort,Variable,FLOWS_TO
getFixVer,Method,Fixver,Variable,FLOWS_TO
getInWardIssueLink,Method,Inwardissuelink,Variable,FLOWS_TO
getLeadTime,Method,Leadtime,Variable,FLOWS_TO
getOrgEst,Method,Orgest,Variable,FLOWS_TO
getOutWardIssueLink,Method,Outwardissuelink,Variable,FLOWS_TO
getpAlmType,Method,Palmtype,Variable,FLOWS_TO
getpName,Method,Pname,Variable,FLOWS_TO
getPriority,Method,Priority,Variable,FLOWS_TO
getRemTime,Method,Remtime,Variable,FLOWS_TO
getResDate,Method,Resdate,Variable,FLOWS_TO
getSeverity,Method,Severity,Variable,FLOWS_TO
getsId,Method,Sid,Variable,FLOWS_TO
getsName,Method,Sname,Variable,FLOWS_TO
getState,Method,State,Variable,FLOWS_TO
getStateSet,Method,Stateset,Variable,FLOWS_TO
getStatusCategory,Method,Statuscategory,Variable,FLOWS_TO
getStoryPoints,Method,Storypoints,Variable,FLOWS_TO
getSubtaskList,Method,Subtasklist,Variable,FLOWS_TO
getSumm,Method,Summ,Variable,FLOWS_TO
getTaskList,Method,Tasklist,Variable,FLOWS_TO
getTransitions,Method,Transitions,Variable,FLOWS_TO
getType,Method,Type,Variable,FLOWS_TO
getUpdatedDate,Method,Updateddate,Variable,FLOWS_TO
getUpdDate,Method,Upddate,Variable,FLOWS_TO
getWaitTime,Method,Waittime,Variable,FLOWS_TO
getwId,Method,Wid,Variable,FLOWS_TO
getTargetRelease,Method,Targetrelease,Variable,FLOWS_TO
getSquads,Method,Squads,Variable,FLOWS_TO
getCategory,Method,Category,Variable,FLOWS_TO
getWhenFound,Method,Whenfound,Variable,FLOWS_TO
getWhereFound,Method,Wherefound,Variable,FLOWS_TO
getHowFound,Method,Howfound,Variable,FLOWS_TO
getEpicIssues,Method,Epicissues,Variable,FLOWS_TO
getProjKey,Method,Projkey,Variable,FLOWS_TO
getMultiSprints,Method,Multisprints,Variable,FLOWS_TO
getEnvironment,Method,Environment,Variable,FLOWS_TO
getAcFeatureOrCapability,Method,Acfeatureorcapability,Variable,FLOWS_TO
getAcFeatureId,Method,Acfeatureid,Variable,FLOWS_TO
getRallyRefURL,Method,Rallyrefurl,Variable,FLOWS_TO
getLabel,Method,Label,Variable,FLOWS_TO
Originalestimate,Variable,getOriginalEstimate,Method,FLOWS_TO
Originalestimate,Variable,Setoriginalestimate(double),Method,FLOWS_TO
Timelogged,Variable,getTimeLogged,Method,FLOWS_TO
Timelogged,Variable,Settimelogged(double),Method,FLOWS_TO
Committedstories,Variable,getCommittedStories,Method,FLOWS_TO
Committedstories,Variable,Setcommittedstories(double),Method,FLOWS_TO
Completedstories,Variable,getCompletedStories,Method,FLOWS_TO
Completedstories,Variable,Setcompletedstories(double),Method,FLOWS_TO
Effort,Variable,Seteffort(double),Method,FLOWS_TO
Defects,Variable,getDefects,Method,FLOWS_TO
Defects,Variable,Setdefects(double),Method,FLOWS_TO
Capacity,Variable,getCapacity,Method,FLOWS_TO
Capacity,Variable,Setcapacity(double),Method,FLOWS_TO
Completeddefects,Variable,getCompletedDefects,Method,FLOWS_TO
Completeddefects,Variable,Setcompleteddefects(double),Method,FLOWS_TO
Startdate,Variable,getStartDate,Method,FLOWS_TO
Startdate,Variable,Setstartdate(long),Method,FLOWS_TO
Enddate,Variable,getEndDate,Method,FLOWS_TO
Enddate,Variable,Setenddate(long),Method,FLOWS_TO
Commitedsp,Variable,getCommitedSp,Method,FLOWS_TO
Commitedsp,Variable,Setcommitedsp(double),Method,FLOWS_TO
Completedsp,Variable,getCompletedSp,Method,FLOWS_TO
Completedsp,Variable,Setcompletedsp(double),Method,FLOWS_TO
Commitedaftersp,Variable,getCommitedAfterSp,Method,FLOWS_TO
Commitedaftersp,Variable,Setcommitedaftersp(double),Method,FLOWS_TO
Refinedsp,Variable,getRefinedSp,Method,FLOWS_TO
Refinedsp,Variable,Setrefinedsp(double),Method,FLOWS_TO
Removedsp,Variable,getRemovedSp,Method,FLOWS_TO
Removedsp,Variable,Setremovedsp(double),Method,FLOWS_TO
Sprintname,Variable,getSprintName,Method,FLOWS_TO
Sprintname,Variable,Setsprintname(string),Method,FLOWS_TO
Sprintid,Variable,getSprintId,Method,FLOWS_TO
Sprintid,Variable,Setsprintid(int),Method,FLOWS_TO
Refineddefects,Variable,getRefinedDefects,Method,FLOWS_TO
Refineddefects,Variable,Setrefineddefects(double),Method,FLOWS_TO
State,Variable,Setstate(string),Method,FLOWS_TO
Spilloversp,Variable,getSpillOverSp,Method,FLOWS_TO
Spilloversp,Variable,Setspilloversp(double),Method,FLOWS_TO
Issuescommited,Variable,getIssuesCommited,Method,FLOWS_TO
Issuescommited,Variable,Setissuescommited(list<issuelist>),Method,FLOWS_TO
Issuescommitedafter,Variable,getIssuesCommitedAfter,Method,FLOWS_TO
Issuescommitedafter,Variable,Setissuescommitedafter(list<issuelist>),Method,FLOWS_TO
Issuescomplted,Variable,getIssuesComplted,Method,FLOWS_TO
Issuescomplted,Variable,Setissuescomplted(list<issuelist>),Method,FLOWS_TO
Issuesremoved,Variable,getIssuesRemoved,Method,FLOWS_TO
Issuesremoved,Variable,Setissuesremoved(list<issuelist>),Method,FLOWS_TO
Issuesrefined,Variable,getIssuesRefined,Method,FLOWS_TO
Issuesrefined,Variable,Setissuesrefined(list<issuelist>),Method,FLOWS_TO
Issuespillover,Variable,getIssueSpillover,Method,FLOWS_TO
Issuespillover,Variable,Setissuespillover(list<issuelist>),Method,FLOWS_TO
Transitionmodel,Variable,Transition,Table,PERSISTS_TO
Setwid(),Method,Set(),Method,CALLS
Setprestatewaittime(),Method,Set(),Method,CALLS
Setcreatetime(),Method,Set(),Method,CALLS
Seteffort(),Method,Set(),Method,CALLS
Setleadtime(),Method,Set(),Method,CALLS
Setcrstate(),Method,Set(),Method,CALLS
Setfrmstate(),Method,Set(),Method,CALLS
Setmdfdate(),Method,Set(),Method,CALLS
Setwaittime(),Method,Set(),Method,CALLS
Setpname(),Method,Set(),Method,CALLS
Setsname(),Method,Set(),Method,CALLS
Setprojkey(),Method,Set(),Method,CALLS
Workitemarr,Variable,getWorkItemArr,Method,FLOWS_TO
Workitemarr,Variable,setWorkItemArr,Method,FLOWS_TO
getWorkItemArr,Method,Workitemarr,Variable,PRODUCES
setWorkItemArr,Method,Workitemarr,Variable,PRODUCES
Projectname,Variable,findByProjectName,Method,FLOWS_TO
Projectname,Variable,deleteByProjectName,Method,FLOWS_TO
findByProjectName,Method,Almconfiguration,Table,READS_FROM
deleteByProjectName,Method,Almconfiguration,Table,WRITES_TO
findByWId,Method,Changehistorymodel,Table,READS_FROM
findByPName,Method,Changehistorymodel,Table,READS_FROM
findByProjectName,Method,Configurationsetting,Table,READS_FROM
findByProjectNameIn,Method,Configurationsetting,Table,READS_FROM
deleteByProjectName,Method,Configurationsetting,Table,WRITES_TO
Sname,Variable,findBySNameAndPName,Method,FLOWS_TO
Sname,Variable,findBySNameAndPNameAndPAlmType,Method,FLOWS_TO
Pname,Variable,findBySNameAndPName,Method,FLOWS_TO
Pname,Variable,findBySNameAndPNameAndPAlmType,Method,FLOWS_TO
Pname,Variable,findByPName,Method,FLOWS_TO
Getsid,Variable,findBySIdAndPName,Method,FLOWS_TO
Projectname,Variable,findBySIdAndPName,Method,FLOWS_TO
findBySNameAndPName,Method,Iterationmodel,Variable,TRANSFORMS_TO
findBySNameAndPNameAndPAlmType,Method,Iterationmodel,Variable,TRANSFORMS_TO
findByPName,Method,List<Iterationmodel>,Variable,TRANSFORMS_TO
findBySIdAndPName,Method,Iterationmodel,Variable,TRANSFORMS_TO
findBySNameAndPName,Method,Iterationmodel,Table,READS_FROM
findBySNameAndPNameAndPAlmType,Method,Iterationmodel,Table,READS_FROM
findByPName,Method,Iterationmodel,Table,READS_FROM
findBySIdAndPName,Method,Iterationmodel,Table,READS_FROM
Iterationrepo,Class,"Get:/Api/Iterations?Filter=Sname,Pname",Endpoint,EXPOSES
findBySNameAndPName,Method,"Get:/Api/Iterations?Filter=Sname,Pname",Endpoint,MAPS_TO
findBySNameAndPNameAndPAlmType,Method,"Get:/Api/Iterations?Filter=Sname,Pname,Palmtype",Endpoint,MAPS_TO
findByPName,Method,Get:/Api/Iterations?Filter=Pname,Endpoint,MAPS_TO
findBySIdAndPName,Method,"Get:/Api/Iterations?Filter=Sid,Pname",Endpoint,MAPS_TO
"Get:/Api/Iterations?Filter=Sname,Pname",Endpoint,Sname,Variable,ACCEPTS
"Get:/Api/Iterations?Filter=Sname,Pname",Endpoint,Pname,Variable,ACCEPTS
"Get:/Api/Iterations?Filter=Sname,Pname,Palmtype",Endpoint,Sname,Variable,ACCEPTS
"Get:/Api/Iterations?Filter:Sname,Pname,Palmtype",Endpoint,Pname,Variable,ACCEPTS
"Get:/Api/Iterations?Filter=Sname,Pname,Palmtype",Endpoint,Palmtype,Variable,ACCEPTS
Get:/Api/Iterations?Filter:Pname,Endpoint,Pname,Variable,ACCEPTS
"Get:/Api/Iterations?Filter:Sid,Pname",Endpoint,Getsid,Variable,ACCEPTS
"Get:/Api/Iterations?Filter:Sid,Pname",Endpoint,Projectname,Variable,ACCEPTS
"Get:/Api/Iterations?Filter:Sname,Pname",Endpoint,Iterationmodel,Variable,RETURNS
"Get:/Api/Iterations?Filter:Sname,Pname,Palmtype",Endpoint,Iterationmodel,Variable,RETURNS
Get:/Api/Iterations?Filter:Pname,Endpoint,List<Iterationmodel>,Variable,RETURNS
"Get:/Api/Iterations?Filter:Sid,Pname",Endpoint,Iterationmodel,Variable,RETURNS
findByPNameAndSName,Method,Metricsmodel,Table,READS_FROM
findByPNameAndType,Method,Metricsmodel,Table,READS_FROM
findByWIdAndPNameAndSName,Method,Metricsmodel,Table,READS_FROM
findByWId,Method,Metricsmodel,Table,READS_FROM
findByPNameAndSId,Method,Metricsmodel,Table,READS_FROM
findByPNameAndTypeAndPAlmType,Method,Metricsmodel,Table,READS_FROM
findByPNameAndSNameAndPAlmType,Method,Metricsmodel,Table,READS_FROM
findByPNameAndSIdAndPAlmType,Method,Metricsmodel,Table,READS_FROM
findByPNameAndSNameAndType,Method,Metricsmodel,Table,READS_FROM
findByPName,Method,Metricsmodel,Table,READS_FROM
findByPNameAndBaselineAndType,Method,Metricsmodel,Table,READS_FROM
findByPNameAndWId,Method,Metricsmodel,Table,READS_FROM
findByPNameAndPAlmType,Method,Metricsmodel,Table,READS_FROM
findByWIdAndPName,Method,Metricsmodel,Table,READS_FROM
Pname,Variable,Metricsmodel,Table,PERSISTS_TO
Sname,Variable,Metricsmodel,Table,PERSISTS_TO
Type,Variable,Metricsmodel,Table,PERSISTS_TO
Wid,Variable,Metricsmodel,Table,PERSISTS_TO
Sid,Variable,Metricsmodel,Table,PERSISTS_TO
Palmtype,Variable,Metricsmodel,Table,PERSISTS_TO
Baseline,Variable,Metricsmodel,Table,PERSISTS_TO
Metricrepo,Class,None,Endpoint,EXPOSES
Projname,Variable,Pname,Variable,FLOWS_TO
findByPNameAndSName,Method,Transitionmodel,Variable,PRODUCES
findByWId,Method,Transitionmodel,Variable,PRODUCES
findByPName,Method,Transitionmodel,Variable,PRODUCES
findByPNameAndSName,Method,Transitionmodel,Table,READS_FROM
findByWId,Method,Transitionmodel,Table,READS_FROM
findByPName,Method,Transitionmodel,Table,READS_FROM
Type,Variable,Almtype,Variable,FLOWS_TO
Key,Variable,Projkey,Variable,FLOWS_TO
Jiraurl,Variable,Releaseurl,Variable,TRANSFORMS_TO
Jiraurl,Variable,Statusurl,Variable,TRANSFORMS_TO
Connection,Method,Externalservice:Jira,Externalservice,TRANSFORMS_TO
Velocityfields,Variable,Storypoints,Variable,TRANSFORMS_TO
Customfieldsjsonarray,Variable,Customobject,Variable,FLOWS_TO
Name,Variable,Key,Variable,TRANSFORMS_TO
Name,Variable,State,Variable,TRANSFORMS_TO
customFieldNames,Class,Get:/Api/Customfieldsinfo,Endpoint,EXPOSES
getCustomFieldsInfo,Method,Get:/Api/Customfieldsinfo,Endpoint,MAPS_TO
getCustomFieldsInfo,Method,Customfieldsjsonarray,Variable,ACCEPTS
getCustomFieldsInfo,Method,Customfieldmap,Variable,RETURNS
customFieldNames,Class,Get:/Api/Projectstatus,Endpoint,EXPOSES
getProjectStatus,Method,Get:/Api/Projectstatus,Endpoint,MAPS_TO
getProjectStatus,Method,Statusarray,Variable,ACCEPTS
getProjectStatus,Method,Taskstatelist,Variable,RETURNS
getCustomFieldsInfo,Method,getCustomFieldsOfLpm,Method,CALLS
getProjectStatus,Method,getTaskStateList,Method,CALLS
getProjectStatus,Method,getSubTaskStateList,Method,CALLS
getProjectStatus,Method,getStoryStateList,Method,CALLS
getProjectStatus,Method,getBugStateList,Method,CALLS
getProjectStatus,Method,getEpicStateList,Method,CALLS
getProjectStatus,Method,getTestStateList,Method,CALLS
getProjectStatus,Method,getStateList,Method,CALLS
getCustomFieldsInfo,Method,Customfieldmap,Variable,PRODUCES
getCustomFieldsOfLpm,Method,Customfiledsoflpm,Variable,PRODUCES
getTaskStateList,Method,Taskstatelist,Variable,PRODUCES
getSubTaskStateList,Method,Subtaskstatelist,Variable,PRODUCES
getStoryStateList,Method,Storystatelist,Variable,PRODUCES
getBugStateList,Method,Bugstatelist,Variable,PRODUCES
getEpicStateList,Method,Epicstatelist,Variable,PRODUCES
getTestStateList,Method,Teststatelist,Variable,PRODUCES
getStateList,Method,Newfeatureexternalstatelist,Variable,PRODUCES
Auth,Variable,Jsonoutput,Variable,PRODUCES
Jsonoutput,Variable,Jsonarrayoutput,Variable,TRANSFORMS_TO
Jsonarrayoutput,Variable,Jsonnew,Variable,TRANSFORMS_TO
Jsonnew,Variable,Wid,Variable,TRANSFORMS_TO
Widlist,Variable,deleteIssues,Method,FLOWS_TO
Tempmetrics,Variable,Recentmodel,Variable,TRANSFORMS_TO
Delete,Method,Metricsmodel,Table,WRITES_TO
deleteIssues,Method,Metricsmodel,Table,WRITES_TO
deleteJiraIssues,Class,Externalservice:Jira,Externalservice,INVOKES
Externalservice:Jira,Externalservice,Jsonoutput,Variable,RETURNS
handleDeletedIssues,Method,jiraConnection,Method,CALLS
handleDeletedIssues,Method,alternateJiraConnection,Method,CALLS
handleDeletedIssues,Method,deleteIssues,Method,CALLS
handleDeletedIssues,Method,Delete,Method,CALLS
Projectname,Variable,Pname,Variable,FLOWS_TO
Sprintname,Variable,Sname,Variable,FLOWS_TO
"Get(""Created"")",Variable,Createddate,Variable,TRANSFORMS_TO
"Get(""Author"")",Variable,Author,Variable,FLOWS_TO
"Get(""Field"")",Variable,Field,Variable,TRANSFORMS_TO
"Get(""To"")",Variable,Newvalue,Variable,TRANSFORMS_TO
"Get(""From"")",Variable,Oldvalue,Variable,TRANSFORMS_TO
Origest,Variable,Initialwork,Variable,TRANSFORMS_TO
"Get(""To"")",Variable,Remainingwork,Variable,TRANSFORMS_TO
Loggedtime,Variable,Timespent,Variable,TRANSFORMS_TO
"Get(""Displayname"")",Variable,Loggedby,Variable,TRANSFORMS_TO
"Get(""Tostring"")",Variable,Epiclink,Variable,TRANSFORMS_TO
populateSubArrays,Method,Objectlist,Variable,PRODUCES
populateSubArrays,Method,timestamp,Method,CALLS
populateSubArrays,Method,Efforthistorymodel,Class,CALLS
populateSubArrays,Method,ChangeHistoryModel,Class,CALLS
EffortAndChangeItemInfo,Class,Rest_Api,Endpoint,EXPOSES
Rest_Api,Endpoint,Json,Variable,ACCEPTS
Allmetrics,Variable,Tempcopy,Variable,FLOWS_TO
Tempissue,Variable,Tempcopy,Variable,FLOWS_TO
Allmetrics,Variable,Epiclinks,Variable,TRANSFORMS_TO
Epiclinks,Variable,Epics,Variable,FLOWS_TO
Epics,Variable,Story,Variable,FLOWS_TO
Tempcopy,Variable,Unmapped,Variable,TRANSFORMS_TO
Story,Variable,Storydata,Variable,TRANSFORMS_TO
Group,Variable,Storydata,Variable,FLOWS_TO
Storydata,Variable,Mappeddata,Variable,FLOWS_TO
Subtask,Variable,Childdata,Variable,TRANSFORMS_TO
Childdata,Variable,Subtasks,Variable,FLOWS_TO
Storylist,Variable,Relateddata,Variable,TRANSFORMS_TO
Linksdata,Variable,Children,Variable,TRANSFORMS_TO
Children,Variable,Getchildren(),Variable,FLOWS_TO
Unmapped,Variable,Unmappedhierarchyview,Variable,TRANSFORMS_TO
Hierarchyview,Variable,"Hierarchymap[""Tracked""]",Variable,FLOWS_TO
Unmappedhierarchyview,Variable,"Hierarchymap[""Untracked""]",Variable,FLOWS_TO
getHierarchy,Method,getUnMappedData,Method,CALLS
getHierarchy,Method,getRelatedTaskInfo,Method,CALLS
getHierarchy,Method,getDataInStructure,Method,CALLS
getRelatedTaskInfo,Method,Copyproperties,Method,CALLS
getUnMappedData,Method,Groupingby,Method,CALLS
getDataInStructure,Method,getRelatedTaskInfo,Method,CALLS
getDataInStructure,Method,Copyproperties,Method,CALLS
getHierarchyData,Method,getHierarchy,Method,CALLS
getHierarchyData,Method,getDataInTreeStructure,Method,CALLS
getHierarchyData,Method,getUnMappedInTreeStructure,Method,CALLS
getDataInTreeStructure,Method,getChildren,Method,CALLS
List<Metricsmodel>,Variable,Allmetrics,Variable,PRODUCES
List<Issuelist>_Mappeddata,Variable,Mappeddata,Variable,PRODUCES
List<Issuelist>_Tempcopy,Variable,Tempcopy,Variable,PRODUCES
Map<String_List<Issuelist>>_Unmapped,Variable,Unmapped,Variable,PRODUCES
Map<String_List<Metricsmodel>>_Epiclinks,Variable,Epiclinks,Variable,PRODUCES
List<String>_Epics,Variable,Epics,Variable,PRODUCES
Metricsmodel,Variable,Issuelist,Variable,FLOWS_TO
Issuelist,Variable,Metricsmodel,Variable,TRANSFORMS_TO
"Customfieldsmap[""Sprint""]",Variable,Tempkey,Variable,FLOWS_TO
Jira_Sprint_Field_Brillio,Variable,Tempkey,Variable,FLOWS_TO
Get(Tempkey),Variable,Sprintjson,Variable,FLOWS_TO
Sprintdata,Variable,Sprintname,Variable,FLOWS_TO
Sprintdata,Variable,Sprintstatus,Variable,FLOWS_TO
Sprintdata,Variable,Startdate,Variable,TRANSFORMS_TO
Sprintdata,Variable,Enddate,Variable,TRANSFORMS_TO
Sprintdata,Variable,Completeddate,Variable,TRANSFORMS_TO
Sprintdata,Variable,Id,Variable,TRANSFORMS_TO
Iteration,Variable,Enddate,Variable,FLOWS_TO
Iteration,Variable,Sprintname,Variable,FLOWS_TO
Iteration,Variable,Startdate,Variable,FLOWS_TO
Iteration,Variable,Projkey,Variable,FLOWS_TO
Iteration,Variable,Palmtype,Variable,FLOWS_TO
Iteration,Variable,Id,Variable,FLOWS_TO
Iteration,Variable,Completeddate,Variable,FLOWS_TO
Iteration,Variable,Sprintstatus,Variable,FLOWS_TO
Iteration,Variable,Projectname,Variable,FLOWS_TO
Iterationset,Variable,Iteration,Variable,FLOWS_TO
Objectlist,Variable,Iterationset,Variable,FLOWS_TO
Objectlist,Variable,Sprintname,Variable,FLOWS_TO
Objectlist,Variable,Sprintid,Variable,FLOWS_TO
Objectlist,Variable,Sprintset,Variable,FLOWS_TO
Objectlist,Variable,Multiplesprints,Variable,FLOWS_TO
populateIteration,Method,getSprintInfo,Method,CALLS
Findbyprojectname(Projectname),Variable,Configurationcolection,Variable,PRODUCES
Getmetrics(),Variable,Metric,Variable,PRODUCES
Iterator(),Variable,Iterator Iter,Variable,PRODUCES
Next(),Variable,Configuration1,Variable,PRODUCES
(Configurationtoolinfometric) Configuration1,Variable,Metric1,Variable,TRANSFORMS_TO
Geturl(),Variable,Instanceurl,Variable,FLOWS_TO
Getusername(),Variable,User,Variable,FLOWS_TO
"Getpassword(), Secretkey)",Variable,Pass,Variable,TRANSFORMS_TO
Getprojectcode(),Variable,Key,Variable,FLOWS_TO
Getreponame(),Variable,Board,Variable,FLOWS_TO
Findbyprojectname(Projectname),Variable,Configurationsetting,Table,READS_FROM
Configurationcolection,Variable,Configurationsetting,Table,PERSISTS_TO
JIRAApplication,Class,Post:/Api/Jira/Main,Endpoint,EXPOSES
jiraMain,Method,Post:/Api/Jira/Main,Endpoint,MAPS_TO
Post:/Api/Jira/Main,Endpoint,Projectname,Variable,ACCEPTS
Post:/Api/Jira/Main,Endpoint,None (Logging Statements),Variable,RETURNS
jiraMain,Method,Getcontext(),Method,CALLS
jiraMain,Method,getALMToolData,Method,CALLS
jiraMain,Method,getLastRun,Method,CALLS
jiraMain,Method,Getdodashboardmetrics,Method,CALLS
jiraMain,Method,cleanObject,Method,CALLS
deleteJiraIssues,Method,Getcontext(),Method,CALLS
deleteJiraIssues,Method,handleDeletedIssues,Method,CALLS
deleteJiraIssues,Method,cleanObject,Method,CALLS
getALMToolData,Method,Externalservice:Jirarestapi,Externalservice,INVOKES
handleDeletedIssues,Method,Externalservice:Jirarestapi,Externalservice,INVOKES
Username,Variable,Loginsearch,Variable,FLOWS_TO
Password,Variable,Loginsearch,Variable,FLOWS_TO
Loginsearch,Variable,Encodedbytes,Variable,TRANSFORMS_TO
Encodedbytes,Variable,Logincreds,Variable,TRANSFORMS_TO
Logincreds,Variable,Basicauth,Variable,TRANSFORMS_TO
Login1,Variable,Encodedbytes,Variable,FLOWS_TO
Teststringnew,Variable,Json Response Object,Variable,TRANSFORMS_TO
JiraAuthentication,Class,Get:/Jiraconnectionforstatus,Endpoint,EXPOSES
jiraConnectionForStatus,Method,Parsed Json,Variable,RETURNS
jiraConnection,Method,Responseentity<String>,Variable,RETURNS
makeRestCall,Method,Responseentity<String>,Variable,RETURNS
Post:/Jiraconnection,Endpoint,Jiraurl,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Username,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Password,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Key,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Start,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Maxresult,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Lastrundate,Variable,ACCEPTS
Post:/Jiraconnection,Endpoint,Deleteflag,Variable,ACCEPTS
Get:/Jiraconnectionforstatus,Endpoint,Jiraurl,Variable,ACCEPTS
Get:/Jiraconnectionforstatus,Endpoint,Username,Variable,ACCEPTS
Get:/Jiraconnectionforstatus,Endpoint,Password,Variable,ACCEPTS
jiraConnectionForStatus,Method,Encodebase64,Method,CALLS
jiraConnectionForStatus,Method,Parse,Method,CALLS
jiraConnection,Method,makeRestCall,Method,CALLS
getBoardFilterId,Method,Encodebase64,Method,CALLS
getBoardFilterId,Method,Parse,Method,CALLS
alternateJiraConnection,Method,Encodebase64,Method,CALLS
alternateJiraConnection,Method,Parse,Method,CALLS
makeRestCall,Method,createHeaders,Method,CALLS
jiraConnectionForStatus,Method,Externalservice:Jirarestapi,Externalservice,INVOKES
jiraConnection,Method,Externalservice:Jirarestapi,Externalservice,INVOKES
alternateJiraConnection,Method,Externalservice:Jirarestapi,Externalservice,INVOKES
getBoardFilterId,Method,Externalservice:Jirarestapi,Externalservice,INVOKES
Jiraclient,Class,Get:/Api/Alm-Tool-Data,Endpoint,EXPOSES
getALMToolData,Method,Get:/Api/Alm-Tool-Data,Endpoint,MAPS_TO
getALMToolData,Method,Urlalm,Variable,ACCEPTS
getALMToolData,Method,Username,Variable,ACCEPTS
getALMToolData,Method,Password,Variable,ACCEPTS
getALMToolData,Method,Projectname,Variable,ACCEPTS
getALMToolData,Method,Key,Variable,ACCEPTS
getALMToolData,Method,Almtype,Variable,ACCEPTS
getALMToolData,Method,Board,Variable,ACCEPTS
getALMToolData,Method,Projectmodel,Variable,RETURNS
JiraExceptions,Method,Message,Variable,PRODUCES
JiraExceptions,Method,Cause,Variable,PRODUCES
Cause,Variable,Initcause(Cause),Variable,TRANSFORMS_TO
Json,Variable,Assignee,Variable,READS_FROM
Assignee,Variable,Displayname,Variable,READS_FROM
Displayname,Variable,Assigneto,Variable,FLOWS_TO
Json,Variable,Summary,Variable,READS_FROM
Summary,Variable,Summ,Variable,TRANSFORMS_TO
Fieldsofjsondata,Variable,Fixversions,Variable,READS_FROM
Fixversions,Variable,Name,Variable,READS_FROM
Fixversions,Variable,Releasedate,Variable,READS_FROM
timestamp,Method,Releasedate,Variable,CALLS
getpName,Method,Metrics,Variable,CALLS
Auth,Variable,Encodedauth,Variable,TRANSFORMS_TO
Encodedauth,Variable,Authheader,Variable,TRANSFORMS_TO
createHeaders,Method,Authheader,Variable,PRODUCES
createHeaders,Method,Headers,Variable,PRODUCES
createAPIHeaders,Method,Headers,Variable,PRODUCES
makeRestCallAPI,Method,Response,Variable,PRODUCES
Response,Variable,Rallyjson,Variable,TRANSFORMS_TO
Rallyjson,Variable,Feature,Variable,TRANSFORMS_TO
Feature,Variable,Arrresult,Variable,TRANSFORMS_TO
makeRestCallAPI,Method,Restapi,Externalservice,INVOKES
RallyAuthentication,Class,Get:/Rally,Endpoint,EXPOSES
Get:/Rally,Endpoint,Url,Variable,ACCEPTS
Get:/Rally,Endpoint,Apikey,Variable,ACCEPTS
callRallyUrl,Method,makeRestCallAPI,Method,CALLS
makeRestCallAPI,Method,get,Method,CALLS
get,Method,Resttemplate,Externalservice,CALLS
createHeaders,Method,Base64,Externalservice,CALLS
callRallyUrl,Method,Jsonparser,Externalservice,CALLS
callRallyUrl,Method,Logger,Method,CALLS
makeRestCallAPI,Method,createAPIHeaders,Method,CALLS
Findbyprojectname(Projectname),Variable,Almconfiguration,Variable,PRODUCES
Almconfiguration,Variable,Gettimezone(),Variable,FLOWS_TO
Almconfiguration,Variable,Getclosestate(),Variable,FLOWS_TO
Releasejsonarray,Variable,Releasejson,Variable,FLOWS_TO
Releasejson,Variable,Release,Variable,FLOWS_TO
Release,Variable,Releaselist,Variable,FLOWS_TO
Releaselist,Variable,Setreleases(),Variable,FLOWS_TO
Releasedetails,Variable,Save(Releasedetails),Variable,FLOWS_TO
Metricslist,Variable,Listit,Variable,FLOWS_TO
Listit,Variable,Metricsmodel,Variable,FLOWS_TO
Iterator(),Variable,Entry,Variable,FLOWS_TO
Time,Variable,Milliseconds,Variable,TRANSFORMS_TO
getReleaseDetails,Method,Releaserepo,Table,READS_FROM
getReleaseDetails,Method,Almconfigrepo,Table,READS_FROM
getReleaseDetails,Method,Releaserepo,Table,WRITES_TO
getReleaseStoryCount,Method,Metricrepo,Table,READS_FROM
getReleaseDetails,Method,getReleaseStoryCount,Method,CALLS
getReleaseDetails,Method,getTime,Method,CALLS
getTime,Method,Gettimezone(),Variable,CALLS
getReleaseStoryCount,Method,Getclosestate(),Variable,CALLS
Pname,Variable,Projectname,Variable,FLOWS_TO
It,Variable,Model,Variable,FLOWS_TO
Startdate,Variable,Model Fields,Variable,TRANSFORMS_TO
Enddate,Variable,Model Fields,Variable,TRANSFORMS_TO
Sprintname,Variable,Model Fields,Variable,TRANSFORMS_TO
Size(),Variable,Mainmodel (Team Size),Variable,TRANSFORMS_TO
Defectscount,Variable,Mainmodel (Defect Counts),Variable,TRANSFORMS_TO
Closeddefectscount,Variable,Mainmodel (Defect Counts),Variable,TRANSFORMS_TO
Openeddefectscount,Variable,Mainmodel (Defect Counts),Variable,TRANSFORMS_TO
Buildfailcount,Variable,Buildfail,Variable,TRANSFORMS_TO
Tasklist,Variable,Objectarray,Variable,TRANSFORMS_TO
Metricslist,Variable,Objectsarray,Variable,FLOWS_TO
Orgestmt,Variable,Objectarray,Variable,TRANSFORMS_TO
Actualeffort,Variable,Objectarray,Variable,TRANSFORMS_TO
Getcollection,Method,Metrics,Table,READS_FROM
Save,Method,Iterationmodel,Table,WRITES_TO
Findbynameandtimestampbetween,Method,Buildtool,Table,READS_FROM
Findbynameandtimestampbetween,Method,Codequality,Table,READS_FROM
Save,Method,Metricsmodel,Table,WRITES_TO
SprintWiseCalculation,Class, Get:/Sprint/Data,Endpoint,EXPOSES
Getsprintdata(stringPname),Method, Get:/Sprint/Data,Endpoint,MAPS_TO
 Get:/Sprint/Data,Endpoint,Pname,Variable,ACCEPTS
getSprintData,Method,storyEffortCalculation,Method,CALLS
getSprintData,Method,getTeamsize,Method,CALLS
getSprintData,Method,getPlannedStoryPoint,Method,CALLS
getSprintData,Method,getTD,Method,CALLS
storyEffortCalculation,Method,getTotalEffort,Method,CALLS
getDefectsSev,Method,aggregate,Method,CALLS
getTeamsize,Method,Getcollection,Method,CALLS
getBuildFailure,Method,Findbynameandtimestampbetween,Method,CALLS
getTD,Method,Findbynameandtimestampbetween,Method,CALLS
getPlannedStoryPoint,Method,findByPNameAndSName,Method,CALLS
getCrtItr,Method,getCurrentItr,Method,CALLS
Transitionmetrices,Variable,Filteredstatusarray,Variable,FLOWS_TO
Transitionmetrices,Variable,Modifieddatelist,Variable,FLOWS_TO
Transitionmetrices,Variable,Tasklaststate,Variable,FLOWS_TO
Transitionmetrices,Variable,Taskeffort,Variable,FLOWS_TO
Transitionmetrices,Variable,Creationtime,Variable,FLOWS_TO
Transitionmetrices,Variable,Taskdetailslist,Variable,FLOWS_TO
Transitionmetrices,Variable,Wid,Variable,FLOWS_TO
Transitionmetrices,Variable,Pname,Variable,FLOWS_TO
Transitionmetrices,Variable,Projkey,Variable,FLOWS_TO
Metric,Variable,Statewaittime,Variable,TRANSFORMS_TO
Modifieddate,Variable,Statewaittime,Variable,TRANSFORMS_TO
Fromstate,Variable,Frmstate,Variable,FLOWS_TO
Tostate,Variable,Crstate,Variable,FLOWS_TO
Modifieddate,Variable,Mdfdate,Variable,FLOWS_TO
Creationtime,Variable,Previousstatewaittime,Variable,FLOWS_TO
Creationtime,Variable,Createtime,Variable,FLOWS_TO
Taskeffort,Variable,Effort,Variable,FLOWS_TO
Statewaittime,Variable,Waittime,Variable,FLOWS_TO
Previousstatewaittime,Variable,Prestatewaittime,Variable,FLOWS_TO
Taskdetails,Variable,Taskdetailslist,Variable,FLOWS_TO
populateTransition,Method,getFilteredStatusArray,Method,CALLS
populateTransition,Method,getModifiedDateList,Method,CALLS
populateTransition,Method,getwId,Method,CALLS
populateTransition,Method,getLastState,Method,CALLS
populateTransition,Method,getEffort,Method,CALLS
populateTransition,Method,getCrTime,Method,CALLS
populateTransition,Method,getFirstState,Method,CALLS
populateTransition,Method,getTaskDetailsList,Method,CALLS
populateTransition,Method,timestamp,Method,CALLS
populateTransition,Method,setWaitTime,Method,CALLS
populateTransition,Method,Add,Method,CALLS
populateTransition,Method,setTaskDetailsList,Method,CALLS
setProjKey,Method,getProjKey,Method,CALLS
setFilteredStatusArray,Method,getFilteredStatusArray,Method,CALLS
setModifiedDateList,Method,getModifiedDateList,Method,CALLS
setwId,Method,getwId,Method,CALLS
setLastState,Method,getLastState,Method,CALLS
setEffort,Method,getEffort,Method,CALLS
setCrTime,Method,getCrTime,Method,CALLS
setFirstState,Method,getFirstState,Method,CALLS
setTaskDetailsList,Method,getTaskDetailsList,Method,CALLS
setpName,Method,getpName,Method,CALLS
setsName,Method,getsName,Method,CALLS
Almconfigrepo,Variable,Almconfiguration,Variable,FLOWS_TO
Almconfiguration,Variable,Closestates,Variable,FLOWS_TO
Metricagedata:Metricagedata,Variable,Issueworkflowsprint,Table,FLOWS_TO
Findbyprojectname(projectname),Method,Almconfiguration,Table,READS_FROM
Findbypname(projectname),Method,Iterationoutmodel,Table,READS_FROM
"Findbypnameandpalmtype(projname,Almtype)",Method,Metricsmodel,Table,READS_FROM
BacklogCalculation,Class,Calculate_Story_Ageing,Endpoint,EXPOSES
caluclateStoryAgeing2,Method,Calculate_Story_Ageing,Endpoint,MAPS_TO
Calculate_Story_Ageing,Endpoint,Projname,Variable,ACCEPTS
Calculate_Story_Ageing,Endpoint,List<Componentstoryageing>,Variable,RETURNS
caluclateStoryAgeing2,Method,getInititialDetails,Method,CALLS
caluclateStoryAgeing2,Method,pushStateFlowObject2,Method,CALLS
caluclateStoryAgeing,Method,getInititialDetails,Method,CALLS
caluclateStoryAgeing,Method,pushStateFlowObject,Method,CALLS
calculateGroomingTable,Method,getComponents,Method,CALLS
Metricagedata,Variable,Issueworkflowsprint,Table,PERSISTS_TO
Reponame,Variable,Findbynameandreponameandbranchname,Method,FLOWS_TO
Projectname,Variable,FindbyBy+primaryValues,Method,FLOWS_TO
Authorlist,Variable,Auth,Variable,FLOWS_TO
getComponents,Method,Component,Variable,FLOWS_TO
Component,Variable,Componentlist,Variable,TRANSFORMS_TO
Time,Variable,Seconds,Variable,TRANSFORMS_TO
Time,Variable,Minutes,Variable,TRANSFORMS_TO
Time,Variable,Hours,Variable,TRANSFORMS_TO
Hours,Variable,Days,Variable,TRANSFORMS_TO
Hours,Variable,Lefthours,Variable,TRANSFORMS_TO
Hours,Variable,Week,Variable,TRANSFORMS_TO
Hours,Variable,Leftdays,Variable,TRANSFORMS_TO
Minutes,Variable,Leftminutes,Variable,TRANSFORMS_TO
Milis,Variable,Seconds,Variable,TRANSFORMS_TO
Milis,Variable,Minutes,Variable,TRANSFORMS_TO
Milis,Variable,Hours,Variable,TRANSFORMS_TO
Milis,Variable,Days,Variable,TRANSFORMS_TO
Milis,Variable,Lefthours,Variable,TRANSFORMS_TO
Seconds,Variable,Minutes,Variable,TRANSFORMS_TO
Seconds,Variable,Hours,Variable,TRANSFORMS_TO
convertToDisplayValues,Method,Valueof,Method,CALLS
convertMilisToDisplayValuesDefect,Method,Valueof,Method,CALLS
convertSecondsToStringDisplay,Method,Valueof,Method,CALLS
toHoursString,Method,Valueof,Method,CALLS
toDaysString,Method,Valueof,Method,CALLS
getComponentList,Method,getMetrics,Method,CALLS
getComponentList,Method,getComponents,Method,CALLS
Prodtypeprior,Variable,Prodtypesev,Variable,TRANSFORMS_TO
Prodtypesev,Variable,Prodtypenewdef,Variable,TRANSFORMS_TO
Prodtypenewdef,Variable,Prodtypewaitdef,Variable,TRANSFORMS_TO
Nonce,Variable,Numbytes,Variable,PRODUCES
Keygen,Variable,Secretkey,Variable,TRANSFORMS_TO
Factory:Keyfactory,Variable,Salt,Variable,TRANSFORMS_TO
Iterations,Variable,Activesprints,Variable,FLOWS_TO
Workingbacklog,Variable,Workingsprints,Variable,FLOWS_TO
Alliterationsandbacklog,Variable,Componentlist,Variable,FLOWS_TO
Tempauthorlist,Variable,Componentwisedata[Component],Variable,FLOWS_TO
Totalbugstable,Variable,Productionslippage,Variable,FLOWS_TO
Totaldefectdata,Variable,Defectsbyseverity,Variable,FLOWS_TO
Almconfiguration,Variable,Closestates,Variable,TRANSFORMS_TO
Iterations,Variable,Filteredsprints,Variable,TRANSFORMS_TO
Defectsadded,Variable,Sprintdefectdensity,Variable,TRANSFORMS_TO
Storypoints,Variable,Totalspcompleted,Variable,TRANSFORMS_TO
Createdate,Variable,Durationdays,Variable,TRANSFORMS_TO
Defectinsightlist,Variable,Defectinsightdata,Variable,PRODUCES
Meantimefix,Variable,"Map<String, String>",Variable,PRODUCES
Groupeddatabymodule,Variable,Groupeddata,Variable,PRODUCES
Category,Variable,List<String>,Variable,PRODUCES
Defectclassification,Variable,List<Map>,Variable,PRODUCES
Defdensity,Variable,List<Defectdensity>,Variable,PRODUCES
Componentwisedata,Variable,"Map<String, Defectbacklog>",Variable,PRODUCES
getInititialDetails,Method,Almconfigrepo,Table,READS_FROM
getInititialDetails,Method,Mongotemplate,Table,READS_FROM
getDefectDensityComp,Method,Metrics,Table,READS_FROM
sprintDefectTrend,Method,Iterationoutmodel,Table,READS_FROM
getDefectBacklogComponent,Method,Monogoutmetrics,Table,READS_FROM
Almconfiguration,Variable,Almconfigrepo,Table,PERSISTS_TO
Totalbugstable,Variable,Metrics,Table,PERSISTS_TO
DefectCalculations,Class,/Api/Defects/{Params},Endpoint,EXPOSES
calculateDefectInsightDataComponent,Method,Post:/Api/Defects/Inventory/Component,Endpoint,MAPS_TO
componentSprintDefectTrend,Method,Get:/Api/Defects/Sprint-Trend,Endpoint,MAPS_TO
getDefectDensityComp,Method,Get:/Api/Defects/Density,Endpoint,MAPS_TO
Post:/Api/Defects/Inventory/Component,Endpoint,Projname,Variable,ACCEPTS
Post:/Api/Defects/Inventory/Component,Endpoint,Almtype,Variable,ACCEPTS
Post:/Api/Defects/Inventory/Component,Endpoint,Defectinsightdata,Variable,RETURNS
calculateDefectInsightDataComponent,Method,getInititialDetails,Method,CALLS
componentSprintDefectTrend,Method,sprintDefectTrend,Method,CALLS
getDefectBacklogComponent,Method,getDefectBacklog,Method,CALLS
calculateDefectInsightData,Method,meanTimeCalculation,Method,CALLS
Dataconfig.Getinstance().Mongotemplate(),Externalservice,Mongotemplate,Table,INVOKES
Ptext,Variable,encrypt,Method,FLOWS_TO
Encrypteddata,Variable,decrypt,Method,FLOWS_TO
EncryptionDecryptionAES,Class,encrypt,Method,EXPOSES
EncryptionDecryptionAES,Class,decrypt,Method,EXPOSES
encrypt,Method,Secret,Variable,ACCEPTS
encrypt,Method,Encrypteddata,Variable,RETURNS
decrypt,Method,Encrypteddata,Variable,ACCEPTS
decrypt,Method,Secretkey,Variable,ACCEPTS
decrypt,Method,Decrypteddata,Variable,RETURNS
encrypt,Method,Getbytes,Method,CALLS
Ptext,Variable,Ciphertext,Variable,TRANSFORMS_TO
Salty,Variable,Aeskeyfrompassword,Variable,TRANSFORMS_TO
Salt,Variable,Ciphertextwithivsalt,Variable,TRANSFORMS_TO
Iv,Variable,Ciphertextwithivsalt,Variable,TRANSFORMS_TO
Ciphertext,Variable,Ciphertextwithivsalt,Variable,TRANSFORMS_TO
Ciphertextwithivsalt,Variable,Encodedstring,Variable,TRANSFORMS_TO
Ctext,Variable,Decode,Variable,TRANSFORMS_TO
Decode,Variable,Iv,Variable,TRANSFORMS_TO
Decode,Variable,Salt,Variable,TRANSFORMS_TO
Decode,Variable,Ciphertext,Variable,TRANSFORMS_TO
Password,Variable,Aeskeyfrompassword,Variable,TRANSFORMS_TO
Ciphertext,Variable,Plaintext,Variable,TRANSFORMS_TO
Plaintext,Variable,Decryptedstring,Variable,TRANSFORMS_TO
encrypt,Method,getRandomNonce,Method,CALLS
encrypt,Method,getAESKeyFromPassword,Method,CALLS
encrypt,Method,Dofinal,Method,CALLS
decrypt,Method,Decode,Method,CALLS
decrypt,Method,getAESKeyFromPassword,Method,CALLS
decrypt,Method,Dofinal,Method,CALLS
encrypt,Method,Salt,Variable,PRODUCES
encrypt,Method,Iv,Variable,PRODUCES
encrypt,Method,Aeskeyfrompassword,Variable,PRODUCES
encrypt,Method,Ciphertext,Variable,PRODUCES
encrypt,Method,Ciphertextwithivsalt,Variable,PRODUCES
encrypt,Method,Encodedstring,Variable,PRODUCES
decrypt,Method,Decode,Variable,PRODUCES
decrypt,Method,Iv,Variable,PRODUCES
decrypt,Method,Salt,Variable,PRODUCES
decrypt,Method,Ciphertext,Variable,PRODUCES
decrypt,Method,Aeskeyfrompassword,Variable,PRODUCES
decrypt,Method,Plaintext,Variable,PRODUCES
decrypt,Method,Decryptedstring,Variable,PRODUCES
Username,Variable,Auth,Variable,FLOWS_TO
Authheader,Variable,"Headers[""Authorization""]",Variable,FLOWS_TO
Urlreal,Variable,Url,Variable,FLOWS_TO
Url,Variable,Urlconnection,Variable,FLOWS_TO
Authstring,Variable,Authencbytes,Variable,TRANSFORMS_TO
Authencbytes,Variable,Authstringenc,Variable,TRANSFORMS_TO
Authstringenc,Variable,Authheader,Variable,FLOWS_TO
Urlconnection,Variable,Is,Variable,FLOWS_TO
Is,Variable,Source,Variable,FLOWS_TO
Source,Variable,Target,Variable,FLOWS_TO
Zipfile,Variable,Zis,Variable,FLOWS_TO
Zis,Variable,Ze,Variable,FLOWS_TO
Getname(),Variable,Filename,Variable,FLOWS_TO
Filename,Variable,Newfile,Variable,FLOWS_TO
Buffer,Variable,Fos,Variable,FLOWS_TO
makeGetRestCall,Method,get,Method,CALLS
makeGetRestCall,Method,createBasicAuthHeaders,Method,CALLS
createBasicAuthHeaders,Method,Httpheaders,Variable,PRODUCES
cleanUp,Method,deleteDirectory,Method,CALLS
downloadXML,Method,unZipIt,Method,CALLS
unZipIt,Method,Close,Method,CALLS
RestClient,Class,Get:/Api/Users,Endpoint,EXPOSES
makeGetRestCall,Method,Get:/Api/Users,Endpoint,MAPS_TO
Get:/Api/Users,Endpoint,Username,Variable,ACCEPTS
Get:/Api/Users,Endpoint,Password,Variable,ACCEPTS
Get:/Api/Users,Endpoint,Url,Variable,ACCEPTS
Get:/Api/Users,Endpoint,Responseentity<String>,Variable,RETURNS
Projname,Variable,"Getinitialdata(projname,Almtype)",Method,FLOWS_TO
Almtype,Variable,"Getinitialdata(projname,Almtype)",Method,FLOWS_TO
Getclosestate(),Method,Closestates,Variable,PRODUCES
),Method,Authordata,Variable,TRANSFORMS_TO
"Add(""all"")",Method,Componentlist,Variable,FLOWS_TO
Getcomponents(projname),Method,Componentlist,Variable,TRANSFORMS_TO
Sort(),Method,Componentlist,Variable,TRANSFORMS_TO
),Method,Stategroupedmetrics,Variable,TRANSFORMS_TO
)),Method,Stategroupedmetrics,Variable,TRANSFORMS_TO
Setassignewisedata(assignewisedatasprint),Method,Taskrisksprintwise,Variable,FLOWS_TO
Metrics,Variable,Getlateststorypoints(monogoutmetricsW),Method,FLOWS_TO
Startdate,Variable,"Getissueriskstatus(total,Closed,Startdate,Enddate)",Method,FLOWS_TO
Enddate,Variable,"Getissueriskstatus(total,Closed,Startdate,Enddate)",Method,FLOWS_TO
"Getinitialdata(projname,Almtype)",Method,Projectiterationrepo.authorrepo,Table,READS_FROM
"Getinitialdata(projname,Almtype)",Method,Almconfigrepo.almconfigrepo,Table,READS_FROM
SprintProgress,Class,Get:/Api/Task-Risk,Endpoint,EXPOSES
"Gettaskrisk(projname,Almtype,Storypointbased)",Method,Get:/Api/Task-Risk,Endpoint,MAPS_TO
Get:/Api/Task-Risk,Endpoint,Projname,Variable,ACCEPTS
Get:/Api/Task-Risk,Endpoint,Almtype,Variable,ACCEPTS
Get:/Api/Task-Risk,Endpoint,Componenttaskrisk,Variable,RETURNS
"Gettaskrisk(projname,Almtype,Storypointbased)",Method,"Getinitialdata(projname,Almtype)",Method,CALLS
"Gettaskrisk(projname,Almtype,Storypointbased)",Method,Getissueriskstorypoint(projname),Method,CALLS
"Gettaskrisk(projname,Almtype,Storypointbased)",Method,Getissueriskeffortbased(projname),Method,CALLS
Getissueriskeffortbased(projname),Method,Getcomponents(projname),Method,CALLS
Getissueriskeffortbased(projname),Method,"Setrequiredvalues(metric,Metricval)",Method,CALLS
Getissueriskeffortbased(projname),Method,"Convertsecondstostringdisplay(orgestimateassignee,8)",Method,CALLS
Getissueriskstorypoint(projname),Method,Getlateststorypoints(w),Method,CALLS
Getissueriskstorypoint(projname),Method,"Setrequiredvalues(metric,Metricval)",Method,CALLS
"Getinitialdata(projname,Almtype)",Method,Getcontext(),Method,CALLS
Getcomponents(projname),Method,Externalservice:Getcomponentsapi,Externalservice,INVOKES
Mongo,Variable,Mongotemplate,Variable,PRODUCES
Ctx,Variable,Annotationconfigapplicationcontext,Variable,PRODUCES
Almconfig,Variable,Getstoryname(),Variable,TRANSFORMS_TO
Almconfig,Variable,Gettaskname(),Variable,TRANSFORMS_TO
Metricrepo,Variable,Allissues,Variable,PRODUCES
StoryProgressModel,Method,Wid,Variable,PRODUCES
StoryProgressModel,Method,Storypoint,Variable,PRODUCES
StoryProgressModel,Method,Successpercentage,Variable,PRODUCES
Storypoint,Variable,getStoryPoint,Method,FLOWS_TO
Successpercentage,Variable,getSuccessPercentage,Method,FLOWS_TO
Storytasks,Variable,Getstorytasks,Variable,FLOWS_TO
Sprintname,Variable,Getsprintname,Variable,FLOWS_TO
"Storyprogresssprintwise(stringSprintname,List<storyprogressmodel>Storytasks)",Method,Sprintname,Variable,PRODUCES
"Storyprogresssprintwise(stringSprintname,List<storyprogressmodel>Storytasks)",Method,Storytasks,Variable,PRODUCES
Setsprintname(stringSprintname),Method,Sprintname,Variable,CALLS
Setstorytasks(list<storyprogressmodel>Storytasks),Method,Storytasks,Variable,CALLS
Sprintname,Variable,Setsprintname,Variable,FLOWS_TO
Startdate,Variable,Getstartdate,Variable,FLOWS_TO
Startdate,Variable,Setstartdate,Variable,FLOWS_TO
Enddate,Variable,Getenddate,Variable,FLOWS_TO
Enddate,Variable,Setenddate,Variable,FLOWS_TO
Estimation,Variable,Getestimation,Variable,FLOWS_TO
Estimation,Variable,Setestimation,Variable,FLOWS_TO
Completed,Variable,Getcompleted,Variable,FLOWS_TO
Completed,Variable,Setcompleted,Variable,FLOWS_TO
Assigneewisetasks,Variable,Getassigneewisetasks,Variable,FLOWS_TO
Assigneewisetasks,Variable,Setassigneewisetasks,Variable,FLOWS_TO
Assignewisedata,Variable,Getassignewisedata,Variable,FLOWS_TO
Assignewisedata,Variable,Setassignewisedata,Variable,FLOWS_TO
Issuecompletionpercentage,Variable,Getissuecompletionpercentage,Variable,FLOWS_TO
Issuecompletionpercentage,Variable,Setissuecompletionpercentage,Variable,FLOWS_TO
Double:First,Variable,Double:(Second - First),Variable,TRANSFORMS_TO
Double:First,Variable,Double:(First - Second),Variable,TRANSFORMS_TO
Boolean:V,Variable,Abs(First - Second),Variable,TRANSFORMS_TO
Double:(Domathresult * 10 / Goalvalue),Variable,Floor(10 - Diff),Variable,TRANSFORMS_TO
Double:(Domathresult / Goalvalue),Variable,Double:(Domathresult/Goalvalue)*10,Variable,TRANSFORMS_TO
calclulateAbsDiff,Method,Returnvalue,Variable,PRODUCES
calclulateDiff,Method,Returnvalue,Variable,PRODUCES
calculatePoints,Method,Returnvalue,Variable,PRODUCES
calculateAbsPoints,Method,Abspoints,Variable,PRODUCES
calculatePoints,Method,Floor,Method,CALLS
calclulateAbsDiff,Method,Abs,Method,CALLS
Double:First,Variable,calclulateAbsDiff,Method,ACCEPTS
Double:Second,Variable,calclulateAbsDiff,Method,ACCEPTS
String:Op,Variable,calclulateAbsDiff,Method,ACCEPTS
Double:First,Variable,calclulateDiff,Method,ACCEPTS
Double:Second,Variable,calclulateDiff,Method,ACCEPTS
String:Op,Variable,calclulateDiff,Method,ACCEPTS
Double:Domathresult,Variable,calculatePoints,Method,ACCEPTS
Double:Goalvalue,Variable,calculatePoints,Method,ACCEPTS
Double:Domathresult,Variable,calculateAbsPoints,Method,ACCEPTS
Double:Goalvalue,Variable,calculateAbsPoints,Method,ACCEPTS
Returnvalue,Variable,calclulateAbsDiff,Method,RETURNS
Returnvalue,Variable,calclulateDiff,Method,RETURNS
Returnvalue,Variable,calculatePoints,Method,RETURNS
Abspoints,Variable,calculateAbsPoints,Method,RETURNS
Iterations,Variable,Auth,Variable,FLOWS_TO
Getstate(),Variable,Tolowercase(),Variable,TRANSFORMS_TO
Workingsprints,Variable,Auth,Variable,FLOWS_TO
Getmetrics(),Variable,Comparing(Iterationoutmodel::Getstdate)),Variable,TRANSFORMS_TO
Getclosestate(),Variable,Closestates,Variable,TRANSFORMS_TO
Actualeffort,Variable,Capacity,Variable,TRANSFORMS_TO
Tempsprefined,Variable,Refinedsp,Variable,TRANSFORMS_TO
Tempspremoved,Variable,Removedsp,Variable,TRANSFORMS_TO
Issuelist,Variable,Issuescommited,Variable,TRANSFORMS_TO
Get(i)),Method,Totalstorypoints,Variable,PRODUCES
Get(i)),Method,Totalcompletedstorypoints,Variable,PRODUCES
Get(i)),Method,Totalspafter,Variable,PRODUCES
"Callspspillover(scorecardsprintprev,Scorecardsprint)",Method,Spilloverlastmonth,Variable,PRODUCES
"Gettransitions(),Date)",Method,Transitionmodel,Variable,PRODUCES
"Checkwithdrawn(m,Enddate)",Method,Checkremoveresult,Variable,PRODUCES
calcVelocity,Method,callSP,Method,CALLS
calcVelocity,Method,calcClosedSP,Method,CALLS
calcVelocity,Method,callSPSpillOver,Method,CALLS
callSP,Method,storyLoop,Method,CALLS
storyLoop,Method,filterTrans,Method,CALLS
storyLoopRefined,Method,checkWithdrawn,Method,CALLS
storyLoopRefined,Method,checkRemoved,Method,CALLS
